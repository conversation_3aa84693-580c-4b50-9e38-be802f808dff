#!/bin/bash

# OpenVPN Certificate Generation Script
# This script generates all necessary certificates for OpenVPN server and clients

set -e

# Configuration
CERT_DIR="$(dirname "$0")"
KEY_SIZE=2048
DAYS=3650
COUNTRY="US"
STATE="CA"
CITY="San Francisco"
ORG="OpenVPN Server"
EMAIL="<EMAIL>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo_error "OpenSSL is not installed. Please install OpenSSL first."
    exit 1
fi

# Create certificate directory if it doesn't exist
mkdir -p "$CERT_DIR"
cd "$CERT_DIR"

echo_info "Generating OpenVPN certificates in $CERT_DIR"

# Generate CA private key
if [ ! -f "ca.key" ]; then
    echo_info "Generating CA private key..."
    openssl genrsa -out ca.key $KEY_SIZE
    chmod 600 ca.key
else
    echo_warn "CA private key already exists, skipping..."
fi

# Generate CA certificate
if [ ! -f "ca.crt" ]; then
    echo_info "Generating CA certificate..."
    openssl req -new -x509 -days $DAYS -key ca.key -out ca.crt -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/CN=OpenVPN CA/emailAddress=$EMAIL"
else
    echo_warn "CA certificate already exists, skipping..."
fi

# Generate server private key
if [ ! -f "server.key" ]; then
    echo_info "Generating server private key..."
    openssl genrsa -out server.key $KEY_SIZE
    chmod 600 server.key
else
    echo_warn "Server private key already exists, skipping..."
fi

# Generate server certificate request
if [ ! -f "server.csr" ]; then
    echo_info "Generating server certificate request..."
    openssl req -new -key server.key -out server.csr -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/CN=OpenVPN Server/emailAddress=$EMAIL"
else
    echo_warn "Server certificate request already exists, skipping..."
fi

# Generate server certificate
if [ ! -f "server.crt" ]; then
    echo_info "Generating server certificate..."
    openssl x509 -req -days $DAYS -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt
    rm server.csr
else
    echo_warn "Server certificate already exists, skipping..."
fi

# Generate Diffie-Hellman parameters
if [ ! -f "dh2048.pem" ]; then
    echo_info "Generating Diffie-Hellman parameters (this may take a while)..."
    openssl dhparam -out dh2048.pem 2048
else
    echo_warn "DH parameters already exist, skipping..."
fi

# Generate TLS auth key
if [ ! -f "ta.key" ]; then
    echo_info "Generating TLS auth key..."
    openvpn --genkey --secret ta.key 2>/dev/null || {
        echo_warn "OpenVPN not found, generating TLS auth key with OpenSSL..."
        openssl rand -hex 256 > ta.key
    }
else
    echo_warn "TLS auth key already exists, skipping..."
fi

# Function to generate client certificate
generate_client_cert() {
    local client_name="$1"
    
    if [ -z "$client_name" ]; then
        echo_error "Client name is required"
        return 1
    fi
    
    echo_info "Generating client certificate for: $client_name"
    
    # Generate client private key
    if [ ! -f "${client_name}.key" ]; then
        openssl genrsa -out "${client_name}.key" $KEY_SIZE
        chmod 600 "${client_name}.key"
    fi
    
    # Generate client certificate request
    openssl req -new -key "${client_name}.key" -out "${client_name}.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/CN=$client_name/emailAddress=$EMAIL"
    
    # Generate client certificate
    openssl x509 -req -days $DAYS -in "${client_name}.csr" -CA ca.crt -CAkey ca.key -CAcreateserial -out "${client_name}.crt"
    
    # Clean up
    rm "${client_name}.csr"
    
    echo_info "Client certificate generated: ${client_name}.crt"
    echo_info "Client private key generated: ${client_name}.key"
}

# Generate a default client certificate
if [ ! -f "client1.crt" ]; then
    generate_client_cert "client1"
fi

# Create client configuration template
cat > client-template.ovpn << 'EOF'
client
dev tun
proto udp
remote YOUR_SERVER_IP 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert CLIENT_NAME.crt
key CLIENT_NAME.key
tls-auth ta.key 1
cipher AES-256-GCM
auth SHA256
verb 3
EOF

echo_info "Certificate generation complete!"
echo_info ""
echo_info "Generated files:"
echo_info "  ca.crt         - Certificate Authority certificate"
echo_info "  ca.key         - Certificate Authority private key"
echo_info "  server.crt     - Server certificate"
echo_info "  server.key     - Server private key"
echo_info "  dh2048.pem     - Diffie-Hellman parameters"
echo_info "  ta.key         - TLS authentication key"
echo_info "  client1.crt    - Sample client certificate"
echo_info "  client1.key    - Sample client private key"
echo_info ""
echo_info "To generate additional client certificates, run:"
echo_info "  $0 client <client_name>"
echo_info ""
echo_info "Security recommendations:"
echo_info "  - Keep ca.key and server.key secure and private"
echo_info "  - Set appropriate file permissions (600 for private keys)"
echo_info "  - Consider using a hardware security module (HSM) for production"

# Handle client certificate generation from command line
if [ "$1" = "client" ] && [ -n "$2" ]; then
    generate_client_cert "$2"
fi
