package network

import (
	"fmt"
	"net"
	"sync"
)

// IPPool manages IP address allocation for VPN clients
type IPPool struct {
	network    *net.IPNet
	serverIP   net.IP
	allocated  map[string]net.IP // clientID -> IP
	available  []net.IP
	mutex      sync.RWMutex
}

// NewIPPool creates a new IP pool
func NewIPPool(networkCIDR string, serverIP net.IP) (*IPPool, error) {
	_, network, err := net.ParseCIDR(networkCIDR)
	if err != nil {
		return nil, fmt.Errorf("invalid network CIDR: %w", err)
	}
	
	pool := &IPPool{
		network:   network,
		serverIP:  serverIP,
		allocated: make(map[string]net.IP),
		available: make([]net.IP, 0),
	}
	
	// Generate available IP addresses
	if err := pool.generateAvailableIPs(); err != nil {
		return nil, fmt.Errorf("failed to generate available IPs: %w", err)
	}
	
	return pool, nil
}

// generateAvailableIPs generates the list of available IP addresses
func (pool *IPPool) generateAvailableIPs() error {
	// Get network address and broadcast address
	networkIP := pool.network.IP
	mask := pool.network.Mask
	
	// Calculate the first and last IP in the range
	firstIP := make(net.IP, len(networkIP))
	copy(firstIP, networkIP)
	
	// Increment first IP by 1 (skip network address)
	incrementIP(firstIP)
	
	// Calculate last IP (broadcast - 1)
	lastIP := make(net.IP, len(networkIP))
	for i := 0; i < len(networkIP); i++ {
		lastIP[i] = networkIP[i] | (^mask[i])
	}
	decrementIP(lastIP) // Skip broadcast address
	
	// Generate all IPs in range
	currentIP := make(net.IP, len(firstIP))
	copy(currentIP, firstIP)
	
	for {
		// Skip server IP
		if !currentIP.Equal(pool.serverIP) {
			ip := make(net.IP, len(currentIP))
			copy(ip, currentIP)
			pool.available = append(pool.available, ip)
		}
		
		// Check if we've reached the last IP
		if currentIP.Equal(lastIP) {
			break
		}
		
		incrementIP(currentIP)
	}
	
	return nil
}

// AllocateIP allocates an IP address for a client
func (pool *IPPool) AllocateIP(clientID string) (net.IP, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()
	
	// Check if client already has an IP
	if ip, exists := pool.allocated[clientID]; exists {
		return ip, nil
	}
	
	// Check if any IPs are available
	if len(pool.available) == 0 {
		return nil, fmt.Errorf("no IP addresses available")
	}
	
	// Allocate the first available IP
	ip := pool.available[0]
	pool.available = pool.available[1:]
	pool.allocated[clientID] = ip
	
	return ip, nil
}

// ReleaseIP releases an IP address from a client
func (pool *IPPool) ReleaseIP(clientID string) error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()
	
	ip, exists := pool.allocated[clientID]
	if !exists {
		return fmt.Errorf("client %s has no allocated IP", clientID)
	}
	
	// Remove from allocated and add back to available
	delete(pool.allocated, clientID)
	pool.available = append(pool.available, ip)
	
	return nil
}

// GetAllocatedIP returns the IP allocated to a client
func (pool *IPPool) GetAllocatedIP(clientID string) (net.IP, bool) {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	
	ip, exists := pool.allocated[clientID]
	return ip, exists
}

// GetAvailableCount returns the number of available IP addresses
func (pool *IPPool) GetAvailableCount() int {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	
	return len(pool.available)
}

// GetAllocatedCount returns the number of allocated IP addresses
func (pool *IPPool) GetAllocatedCount() int {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	
	return len(pool.allocated)
}

// GetNetworkInfo returns network information
func (pool *IPPool) GetNetworkInfo() (*net.IPNet, net.IP) {
	return pool.network, pool.serverIP
}

// incrementIP increments an IP address by 1
func incrementIP(ip net.IP) {
	for i := len(ip) - 1; i >= 0; i-- {
		ip[i]++
		if ip[i] != 0 {
			break
		}
	}
}

// decrementIP decrements an IP address by 1
func decrementIP(ip net.IP) {
	for i := len(ip) - 1; i >= 0; i-- {
		if ip[i] != 0 {
			ip[i]--
			break
		}
		ip[i] = 255
	}
}

// RouteManager manages routing for VPN clients
type RouteManager struct {
	routes map[string]*Route // clientID -> routes
	mutex  sync.RWMutex
}

// Route represents a network route
type Route struct {
	Destination *net.IPNet
	Gateway     net.IP
	Interface   string
	Metric      int
}

// NewRouteManager creates a new route manager
func NewRouteManager() *RouteManager {
	return &RouteManager{
		routes: make(map[string]*Route),
	}
}

// AddRoute adds a route for a client
func (rm *RouteManager) AddRoute(clientID string, destination *net.IPNet, gateway net.IP, iface string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	route := &Route{
		Destination: destination,
		Gateway:     gateway,
		Interface:   iface,
		Metric:      100,
	}
	
	rm.routes[clientID] = route
	
	// In a real implementation, this would add the route to the system routing table
	return rm.addSystemRoute(route)
}

// RemoveRoute removes a route for a client
func (rm *RouteManager) RemoveRoute(clientID string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	route, exists := rm.routes[clientID]
	if !exists {
		return fmt.Errorf("no route found for client %s", clientID)
	}
	
	delete(rm.routes, clientID)
	
	// In a real implementation, this would remove the route from the system routing table
	return rm.removeSystemRoute(route)
}

// GetRoute returns the route for a client
func (rm *RouteManager) GetRoute(clientID string) (*Route, bool) {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	route, exists := rm.routes[clientID]
	return route, exists
}

// addSystemRoute adds a route to the system routing table
func (rm *RouteManager) addSystemRoute(route *Route) error {
	// This is a placeholder implementation
	// In a real implementation, this would use system calls or commands
	// to add routes to the operating system's routing table
	
	fmt.Printf("Adding route: %s via %s dev %s\n", 
		route.Destination.String(), route.Gateway.String(), route.Interface)
	
	return nil
}

// removeSystemRoute removes a route from the system routing table
func (rm *RouteManager) removeSystemRoute(route *Route) error {
	// This is a placeholder implementation
	// In a real implementation, this would use system calls or commands
	// to remove routes from the operating system's routing table
	
	fmt.Printf("Removing route: %s via %s dev %s\n", 
		route.Destination.String(), route.Gateway.String(), route.Interface)
	
	return nil
}

// NATManager manages NAT rules for VPN traffic
type NATManager struct {
	rules map[string]*NATRule // clientID -> NAT rule
	mutex sync.RWMutex
}

// NATRule represents a NAT rule
type NATRule struct {
	SourceIP      net.IP
	SourceNetwork *net.IPNet
	OutInterface  string
	Masquerade    bool
}

// NewNATManager creates a new NAT manager
func NewNATManager() *NATManager {
	return &NATManager{
		rules: make(map[string]*NATRule),
	}
}

// AddNATRule adds a NAT rule for a client
func (nm *NATManager) AddNATRule(clientID string, sourceIP net.IP, sourceNet *net.IPNet, outIface string) error {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()
	
	rule := &NATRule{
		SourceIP:      sourceIP,
		SourceNetwork: sourceNet,
		OutInterface:  outIface,
		Masquerade:    true,
	}
	
	nm.rules[clientID] = rule
	
	// In a real implementation, this would add iptables rules or equivalent
	return nm.addSystemNATRule(rule)
}

// RemoveNATRule removes a NAT rule for a client
func (nm *NATManager) RemoveNATRule(clientID string) error {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()
	
	rule, exists := nm.rules[clientID]
	if !exists {
		return fmt.Errorf("no NAT rule found for client %s", clientID)
	}
	
	delete(nm.rules, clientID)
	
	// In a real implementation, this would remove iptables rules or equivalent
	return nm.removeSystemNATRule(rule)
}

// addSystemNATRule adds a NAT rule to the system
func (nm *NATManager) addSystemNATRule(rule *NATRule) error {
	// This is a placeholder implementation
	// In a real implementation, this would use iptables or equivalent
	
	fmt.Printf("Adding NAT rule: MASQUERADE %s -> %s\n", 
		rule.SourceNetwork.String(), rule.OutInterface)
	
	return nil
}

// removeSystemNATRule removes a NAT rule from the system
func (nm *NATManager) removeSystemNATRule(rule *NATRule) error {
	// This is a placeholder implementation
	// In a real implementation, this would use iptables or equivalent
	
	fmt.Printf("Removing NAT rule: MASQUERADE %s -> %s\n", 
		rule.SourceNetwork.String(), rule.OutInterface)
	
	return nil
}
