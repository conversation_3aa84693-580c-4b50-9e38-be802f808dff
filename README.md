# OpenVPN Server Implementation in Go

A Go implementation of an OpenVPN-compatible VPN server that supports standard OpenVPN clients.

## Features

- **OpenVPN Protocol Compatibility**: Supports standard OpenVPN client connections
- **Secure Communication**: TLS handshake and data channel encryption
- **Authentication**: Certificate-based and username/password authentication
- **Network Management**: TUN/TAP interface handling and IP allocation
- **Configuration**: OpenVPN-style configuration file support
- **Monitoring**: Comprehensive logging and connection monitoring
- **Cross-Platform**: Supports Linux, macOS, and Windows
- **High Performance**: Efficient packet processing and connection handling

## Project Structure

```
├── cmd/
│   └── server/          # Main server application
├── internal/
│   ├── config/          # Configuration management
│   ├── crypto/          # Cryptography and TLS handling
│   ├── network/         # Network interface and routing
│   ├── protocol/        # OpenVPN protocol implementation
│   └── server/          # Core server logic
├── pkg/
│   └── auth/            # Authentication mechanisms
├── configs/             # Example configuration files
├── certs/               # Certificate management utilities
└── docs/                # Documentation
```

## Quick Start

### 1. Build the Server

```bash
# Clone the repository
git clone <repository-url>
cd openvpn-server

# Build the server
go build -o openvpn-server ./cmd/server
```

### 2. Generate Certificates

```bash
# Make the certificate generation script executable
chmod +x certs/generate-certs.sh

# Generate all necessary certificates
cd certs
./generate-certs.sh

# Generate additional client certificates
./generate-certs.sh client alice
./generate-certs.sh client bob
```

### 3. Configure the Server

Edit `configs/server.conf` to match your environment:

```bash
# Basic configuration
port 1194
proto udp
dev tun

# Certificate files
ca certs/ca.crt
cert certs/server.crt
key certs/server.key
dh certs/dh2048.pem

# Network settings
server ******** *************
```

### 4. Run the Server

```bash
# Run with default configuration
sudo ./openvpn-server

# Run with custom configuration
sudo ./openvpn-server -config configs/server.conf

# Run with debug logging
sudo ./openvpn-server -config configs/server.conf -log-level debug
```

## Configuration

### Server Configuration

The server uses OpenVPN-style configuration files. Key directives include:

```bash
# Network settings
port 1194                    # Port to listen on
proto udp                    # Protocol (udp or tcp)
local 0.0.0.0               # IP address to bind to
dev tun                     # Device type (tun or tap)

# VPN network
server ******** *************  # VPN subnet

# Certificates
ca ca.crt                   # Certificate Authority
cert server.crt             # Server certificate
key server.key              # Server private key
dh dh2048.pem               # Diffie-Hellman parameters

# Security
cipher AES-256-GCM          # Encryption cipher
auth SHA256                 # HMAC authentication
tls-auth ta.key 0           # TLS authentication

# Client settings
max-clients 100             # Maximum concurrent clients
keepalive 10 120            # Keepalive settings

# Push routes to clients
push "route *********** *************"
push "dhcp-option DNS *******"

# Logging
log /var/log/openvpn.log
verb 3
```

### Client Configuration

Example client configuration (`client.ovpn`):

```bash
client
dev tun
proto udp
remote your-server-ip 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Certificates
ca ca.crt
cert client.crt
key client.key
tls-auth ta.key 1

# Security
cipher AES-256-GCM
auth SHA256
verb 3
```

## Authentication

### Certificate-Based Authentication

The server supports X.509 certificate authentication:

1. **Generate CA and server certificates** (see `certs/` directory)
2. **Generate client certificates** for each user
3. **Configure certificate paths** in server configuration
4. **Distribute client certificates** securely

### Username/Password Authentication

Enable username/password authentication:

```bash
# In server configuration
auth-user-pass-verify /path/to/auth-script via-env
```

Create an authentication script that validates credentials.

### Combined Authentication

Use both certificates and username/password:

```bash
# Require both certificate and username/password
auth-user-pass-verify /path/to/auth-script via-env
# Certificate validation is always performed
```

## Network Configuration

### TUN/TAP Interface

The server creates a virtual network interface:

- **TUN**: Layer 3 (IP) tunneling (recommended)
- **TAP**: Layer 2 (Ethernet) tunneling

### IP Address Management

- Server automatically allocates IP addresses from the configured subnet
- Clients receive IP configuration via DHCP-like push options
- IP addresses are managed per-client session

### Routing

Configure routing for VPN traffic:

```bash
# Push routes to clients
push "route *********** *************"
push "route 10.0.0.0 *********"

# Redirect all traffic through VPN
push "redirect-gateway def1"

# Allow client-to-client communication
client-to-client
```

## Security Features

### Encryption

- **Control Channel**: TLS 1.2+ with strong cipher suites
- **Data Channel**: AES-GCM, ChaCha20-Poly1305, or AES-CBC with HMAC
- **Perfect Forward Secrecy**: Regular key rotation

### Authentication

- **Certificate Validation**: X.509 certificate chain validation
- **Username/Password**: External script-based authentication
- **Multi-Factor**: Support for additional authentication factors

### Protection

- **Replay Protection**: Packet sequence number validation
- **Traffic Analysis**: Encrypted tunnels prevent traffic inspection
- **DoS Protection**: Rate limiting and connection management

## Monitoring and Logging

### Logging Levels

- **0**: No output except fatal errors
- **1**: Startup/shutdown messages and errors
- **3**: Normal usage (recommended)
- **5**: Medium verbosity for debugging
- **9**: Maximum verbosity for troubleshooting

### Log Files

Configure logging destinations:

```bash
# Log to file
log /var/log/openvpn.log

# Log to syslog
syslog

# Set verbosity level
verb 3
```

### Management Interface

Enable management interface for monitoring:

```bash
management 127.0.0.1 7505
```

Connect to management interface:

```bash
telnet 127.0.0.1 7505
```

## Testing

### Run Unit Tests

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific package tests
go test ./internal/protocol/
```

### Integration Testing

1. **Start the server** with test configuration
2. **Connect with OpenVPN client** using generated certificates
3. **Verify connectivity** and traffic flow
4. **Test authentication** mechanisms

### Performance Testing

```bash
# Load testing with multiple clients
# Monitor resource usage
# Measure throughput and latency
```

## Deployment

### System Requirements

- **Operating System**: Linux, macOS, or Windows
- **Privileges**: Root/Administrator access for TUN/TAP interfaces
- **Network**: Firewall configuration for VPN port
- **Resources**: Adequate CPU and memory for expected client load

### Production Deployment

1. **Security Hardening**
   - Use strong certificates with proper key management
   - Configure firewall rules
   - Enable logging and monitoring
   - Regular security updates

2. **High Availability**
   - Multiple server instances
   - Load balancing
   - Session persistence
   - Failover mechanisms

3. **Monitoring**
   - Health checks
   - Performance metrics
   - Log aggregation
   - Alerting

### Docker Deployment

```dockerfile
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o openvpn-server ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/openvpn-server .
COPY configs/ configs/
COPY certs/ certs/
CMD ["./openvpn-server"]
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Run with root privileges
   - Check TUN/TAP device permissions

2. **Certificate Errors**
   - Verify certificate validity and chain
   - Check file permissions and paths

3. **Connection Timeouts**
   - Verify firewall configuration
   - Check network connectivity
   - Review server logs

4. **Authentication Failures**
   - Validate certificates
   - Check authentication scripts
   - Review authentication logs

### Debug Mode

Enable debug logging for troubleshooting:

```bash
sudo ./openvpn-server -config configs/server.conf -log-level debug
```

### Log Analysis

Common log patterns to look for:

- Connection attempts and results
- Authentication success/failure
- Certificate validation errors
- Network interface issues
- Encryption/decryption errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## Requirements

- **Go**: 1.19 or later
- **OpenSSL**: For certificate generation
- **Root Privileges**: For TUN/TAP interface creation
- **Network Access**: Appropriate firewall configuration

## License

MIT License - see LICENSE file for details.

## Support

- **Documentation**: See `docs/` directory
- **Issues**: Report bugs and feature requests via GitHub issues
- **Security**: Report security issues privately to maintainers
