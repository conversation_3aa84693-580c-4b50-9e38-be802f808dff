package protocol

import (
	"encoding/binary"
	"fmt"
)

// OpenVPN Protocol Constants
const (
	// Packet opcodes
	P_CONTROL_HARD_RESET_CLIENT_V1 = 1
	P_CONTROL_HARD_RESET_SERVER_V1 = 2
	P_CONTROL_SOFT_RESET_V1        = 3
	P_CONTROL_V1                   = 4
	P_ACK_V1                       = 5
	P_DATA_V1                      = 6
	P_CONTROL_HARD_RESET_CLIENT_V2 = 7
	P_CONTROL_HARD_RESET_SERVER_V2 = 8
	P_DATA_V2                      = 9
	
	// Key methods
	KEY_METHOD_1 = 1
	KEY_METHOD_2 = 2
	
	// Maximum packet sizes
	MAX_PACKET_SIZE = 1500
	MAX_CONTROL_SIZE = 1024
	
	// Session ID length
	SESSION_ID_LENGTH = 8
)

// PacketHeader represents the OpenVPN packet header
type PacketHeader struct {
	Opcode    uint8  // Packet opcode
	KeyID     uint8  // Key ID (0-7)
	SessionID []byte // Session ID (8 bytes)
	PacketID  uint32 // Packet ID for replay protection
}

// Packet represents an OpenVPN packet
type Packet struct {
	Header  PacketHeader
	Payload []byte
}

// ControlMessage represents a control channel message
type ControlMessage struct {
	MessageType uint8
	Data        []byte
}

// DataPacket represents a data channel packet
type DataPacket struct {
	Header PacketHeader
	Data   []byte
}

// ParsePacket parses a raw packet into a Packet structure
func ParsePacket(data []byte) (*Packet, error) {
	if len(data) < 10 { // Minimum packet size (opcode + key_id + session_id)
		return nil, fmt.Errorf("packet too short: %d bytes", len(data))
	}
	
	packet := &Packet{}
	offset := 0
	
	// Parse opcode and key ID (combined in first byte)
	opcodeKeyID := data[offset]
	packet.Header.Opcode = (opcodeKeyID >> 3) & 0x1F // Upper 5 bits
	packet.Header.KeyID = opcodeKeyID & 0x07         // Lower 3 bits
	offset++
	
	// Parse session ID (8 bytes)
	if len(data) < offset+SESSION_ID_LENGTH {
		return nil, fmt.Errorf("packet too short for session ID")
	}
	packet.Header.SessionID = make([]byte, SESSION_ID_LENGTH)
	copy(packet.Header.SessionID, data[offset:offset+SESSION_ID_LENGTH])
	offset += SESSION_ID_LENGTH
	
	// For control packets, parse packet ID
	if IsControlPacket(packet.Header.Opcode) {
		if len(data) < offset+4 {
			return nil, fmt.Errorf("packet too short for packet ID")
		}
		packet.Header.PacketID = binary.BigEndian.Uint32(data[offset:offset+4])
		offset += 4
	}
	
	// Remaining data is payload
	if offset < len(data) {
		packet.Payload = make([]byte, len(data)-offset)
		copy(packet.Payload, data[offset:])
	}
	
	return packet, nil
}

// SerializePacket serializes a packet to bytes
func (p *Packet) SerializePacket() ([]byte, error) {
	// Calculate total size
	size := 1 + SESSION_ID_LENGTH // opcode/keyid + session_id
	if IsControlPacket(p.Header.Opcode) {
		size += 4 // packet_id
	}
	size += len(p.Payload)
	
	if size > MAX_PACKET_SIZE {
		return nil, fmt.Errorf("packet too large: %d bytes", size)
	}
	
	data := make([]byte, size)
	offset := 0
	
	// Serialize opcode and key ID
	data[offset] = (p.Header.Opcode << 3) | (p.Header.KeyID & 0x07)
	offset++
	
	// Serialize session ID
	copy(data[offset:offset+SESSION_ID_LENGTH], p.Header.SessionID)
	offset += SESSION_ID_LENGTH
	
	// For control packets, serialize packet ID
	if IsControlPacket(p.Header.Opcode) {
		binary.BigEndian.PutUint32(data[offset:offset+4], p.Header.PacketID)
		offset += 4
	}
	
	// Serialize payload
	if len(p.Payload) > 0 {
		copy(data[offset:], p.Payload)
	}
	
	return data, nil
}

// IsControlPacket returns true if the opcode represents a control packet
func IsControlPacket(opcode uint8) bool {
	switch opcode {
	case P_CONTROL_HARD_RESET_CLIENT_V1,
		 P_CONTROL_HARD_RESET_SERVER_V1,
		 P_CONTROL_SOFT_RESET_V1,
		 P_CONTROL_V1,
		 P_ACK_V1,
		 P_CONTROL_HARD_RESET_CLIENT_V2,
		 P_CONTROL_HARD_RESET_SERVER_V2:
		return true
	default:
		return false
	}
}

// IsDataPacket returns true if the opcode represents a data packet
func IsDataPacket(opcode uint8) bool {
	switch opcode {
	case P_DATA_V1, P_DATA_V2:
		return true
	default:
		return false
	}
}

// IsResetPacket returns true if the opcode represents a reset packet
func IsResetPacket(opcode uint8) bool {
	switch opcode {
	case P_CONTROL_HARD_RESET_CLIENT_V1,
		 P_CONTROL_HARD_RESET_SERVER_V1,
		 P_CONTROL_HARD_RESET_CLIENT_V2,
		 P_CONTROL_HARD_RESET_SERVER_V2:
		return true
	default:
		return false
	}
}

// GetOpcodeString returns a string representation of the opcode
func GetOpcodeString(opcode uint8) string {
	switch opcode {
	case P_CONTROL_HARD_RESET_CLIENT_V1:
		return "P_CONTROL_HARD_RESET_CLIENT_V1"
	case P_CONTROL_HARD_RESET_SERVER_V1:
		return "P_CONTROL_HARD_RESET_SERVER_V1"
	case P_CONTROL_SOFT_RESET_V1:
		return "P_CONTROL_SOFT_RESET_V1"
	case P_CONTROL_V1:
		return "P_CONTROL_V1"
	case P_ACK_V1:
		return "P_ACK_V1"
	case P_DATA_V1:
		return "P_DATA_V1"
	case P_CONTROL_HARD_RESET_CLIENT_V2:
		return "P_CONTROL_HARD_RESET_CLIENT_V2"
	case P_CONTROL_HARD_RESET_SERVER_V2:
		return "P_CONTROL_HARD_RESET_SERVER_V2"
	case P_DATA_V2:
		return "P_DATA_V2"
	default:
		return fmt.Sprintf("UNKNOWN_OPCODE_%d", opcode)
	}
}

// NewControlPacket creates a new control packet
func NewControlPacket(opcode uint8, keyID uint8, sessionID []byte, packetID uint32, payload []byte) *Packet {
	return &Packet{
		Header: PacketHeader{
			Opcode:    opcode,
			KeyID:     keyID,
			SessionID: sessionID,
			PacketID:  packetID,
		},
		Payload: payload,
	}
}

// NewDataPacket creates a new data packet
func NewDataPacket(keyID uint8, sessionID []byte, data []byte) *Packet {
	return &Packet{
		Header: PacketHeader{
			Opcode:    P_DATA_V1,
			KeyID:     keyID,
			SessionID: sessionID,
		},
		Payload: data,
	}
}

// NewAckPacket creates a new ACK packet
func NewAckPacket(keyID uint8, sessionID []byte, packetID uint32) *Packet {
	return &Packet{
		Header: PacketHeader{
			Opcode:    P_ACK_V1,
			KeyID:     keyID,
			SessionID: sessionID,
			PacketID:  packetID,
		},
		Payload: nil,
	}
}
