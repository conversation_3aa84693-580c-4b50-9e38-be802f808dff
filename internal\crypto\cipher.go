package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"fmt"
)

// CipherSuite represents an encryption cipher suite
type CipherSuite struct {
	Name       string
	KeySize    int    // Key size in bytes
	IVSize     int    // IV size in bytes
	BlockSize  int    // Block size in bytes
	AuthSize   int    // Authentication tag size in bytes
	IsAEAD     bool   // Whether this is an AEAD cipher
}

// Supported cipher suites
var SupportedCiphers = map[string]*CipherSuite{
	"AES-256-GCM": {
		Name:      "AES-256-GCM",
		KeySize:   32,
		IVSize:    12,
		BlockSize: 16,
		AuthSize:  16,
		IsAEAD:    true,
	},
	"AES-128-GCM": {
		Name:      "AES-128-GCM",
		KeySize:   16,
		IVSize:    12,
		BlockSize: 16,
		AuthSize:  16,
		IsAEAD:    true,
	},
	"AES-256-CBC": {
		Name:      "AES-256-CBC",
		KeySize:   32,
		IVSize:    16,
		BlockSize: 16,
		AuthSize:  0,
		IsAEAD:    false,
	},
	"AES-128-CBC": {
		Name:      "AES-128-CBC",
		KeySize:   16,
		IVSize:    16,
		BlockSize: 16,
		AuthSize:  0,
		IsAEAD:    false,
	},
}

// DataChannelCrypto handles encryption/decryption for the data channel
type DataChannelCrypto struct {
	cipher     *CipherSuite
	encryptKey []byte
	decryptKey []byte
	hmacKey    []byte
	sendIV     []byte
	recvIV     []byte
}

// NewDataChannelCrypto creates a new data channel crypto instance
func NewDataChannelCrypto(cipherName string, keyMaterial []byte) (*DataChannelCrypto, error) {
	cipher, exists := SupportedCiphers[cipherName]
	if !exists {
		return nil, fmt.Errorf("unsupported cipher: %s", cipherName)
	}
	
	// Key material should be at least 2 * keySize + 2 * ivSize + hmacKeySize
	requiredSize := 2*cipher.KeySize + 2*cipher.IVSize + 32 // 32 bytes for HMAC key
	if len(keyMaterial) < requiredSize {
		return nil, fmt.Errorf("insufficient key material: need %d bytes, got %d", requiredSize, len(keyMaterial))
	}
	
	dcc := &DataChannelCrypto{
		cipher: cipher,
	}
	
	// Derive keys from key material
	offset := 0
	
	// Encrypt key (client->server)
	dcc.encryptKey = make([]byte, cipher.KeySize)
	copy(dcc.encryptKey, keyMaterial[offset:offset+cipher.KeySize])
	offset += cipher.KeySize
	
	// Decrypt key (server->client)
	dcc.decryptKey = make([]byte, cipher.KeySize)
	copy(dcc.decryptKey, keyMaterial[offset:offset+cipher.KeySize])
	offset += cipher.KeySize
	
	// Send IV
	dcc.sendIV = make([]byte, cipher.IVSize)
	copy(dcc.sendIV, keyMaterial[offset:offset+cipher.IVSize])
	offset += cipher.IVSize
	
	// Receive IV
	dcc.recvIV = make([]byte, cipher.IVSize)
	copy(dcc.recvIV, keyMaterial[offset:offset+cipher.IVSize])
	offset += cipher.IVSize
	
	// HMAC key (if not AEAD)
	if !cipher.IsAEAD {
		dcc.hmacKey = make([]byte, 32)
		copy(dcc.hmacKey, keyMaterial[offset:offset+32])
	}
	
	return dcc, nil
}

// Encrypt encrypts data for transmission
func (dcc *DataChannelCrypto) Encrypt(plaintext []byte) ([]byte, error) {
	switch dcc.cipher.Name {
	case "AES-256-GCM", "AES-128-GCM":
		return dcc.encryptGCM(plaintext, dcc.encryptKey)
	case "AES-256-CBC", "AES-128-CBC":
		return dcc.encryptCBC(plaintext, dcc.encryptKey)
	default:
		return nil, fmt.Errorf("unsupported cipher for encryption: %s", dcc.cipher.Name)
	}
}

// Decrypt decrypts received data
func (dcc *DataChannelCrypto) Decrypt(ciphertext []byte) ([]byte, error) {
	switch dcc.cipher.Name {
	case "AES-256-GCM", "AES-128-GCM":
		return dcc.decryptGCM(ciphertext, dcc.decryptKey)
	case "AES-256-CBC", "AES-128-CBC":
		return dcc.decryptCBC(ciphertext, dcc.decryptKey)
	default:
		return nil, fmt.Errorf("unsupported cipher for decryption: %s", dcc.cipher.Name)
	}
}

// encryptGCM encrypts using AES-GCM
func (dcc *DataChannelCrypto) encryptGCM(plaintext []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}
	
	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}
	
	// Encrypt and authenticate
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)
	
	// Prepend nonce to ciphertext
	result := make([]byte, len(nonce)+len(ciphertext))
	copy(result, nonce)
	copy(result[len(nonce):], ciphertext)
	
	return result, nil
}

// decryptGCM decrypts using AES-GCM
func (dcc *DataChannelCrypto) decryptGCM(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}
	
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	
	nonce := data[:nonceSize]
	ciphertext := data[nonceSize:]
	
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}
	
	return plaintext, nil
}

// encryptCBC encrypts using AES-CBC with HMAC
func (dcc *DataChannelCrypto) encryptCBC(plaintext []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}
	
	// Add PKCS7 padding
	paddedPlaintext := addPKCS7Padding(plaintext, block.BlockSize())
	
	// Generate random IV
	iv := make([]byte, block.BlockSize())
	if _, err := rand.Read(iv); err != nil {
		return nil, fmt.Errorf("failed to generate IV: %w", err)
	}
	
	// Encrypt
	ciphertext := make([]byte, len(paddedPlaintext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, paddedPlaintext)
	
	// Calculate HMAC
	mac := hmac.New(sha256.New, dcc.hmacKey)
	mac.Write(iv)
	mac.Write(ciphertext)
	hmacSum := mac.Sum(nil)
	
	// Combine IV + ciphertext + HMAC
	result := make([]byte, len(iv)+len(ciphertext)+len(hmacSum))
	copy(result, iv)
	copy(result[len(iv):], ciphertext)
	copy(result[len(iv)+len(ciphertext):], hmacSum)
	
	return result, nil
}

// decryptCBC decrypts using AES-CBC with HMAC verification
func (dcc *DataChannelCrypto) decryptCBC(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}
	
	ivSize := block.BlockSize()
	hmacSize := 32 // SHA256 HMAC size
	
	if len(data) < ivSize+hmacSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	
	iv := data[:ivSize]
	ciphertext := data[ivSize : len(data)-hmacSize]
	receivedHMAC := data[len(data)-hmacSize:]
	
	// Verify HMAC
	mac := hmac.New(sha256.New, dcc.hmacKey)
	mac.Write(iv)
	mac.Write(ciphertext)
	expectedHMAC := mac.Sum(nil)
	
	if !hmac.Equal(receivedHMAC, expectedHMAC) {
		return nil, fmt.Errorf("HMAC verification failed")
	}
	
	// Decrypt
	plaintext := make([]byte, len(ciphertext))
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(plaintext, ciphertext)
	
	// Remove PKCS7 padding
	unpaddedPlaintext, err := removePKCS7Padding(plaintext)
	if err != nil {
		return nil, fmt.Errorf("failed to remove padding: %w", err)
	}
	
	return unpaddedPlaintext, nil
}

// addPKCS7Padding adds PKCS7 padding to data
func addPKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	return append(data, padText...)
}

// removePKCS7Padding removes PKCS7 padding from data
func removePKCS7Padding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}
	
	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, fmt.Errorf("invalid padding")
	}
	
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, fmt.Errorf("invalid padding")
		}
	}
	
	return data[:len(data)-padding], nil
}
