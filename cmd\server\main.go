package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"openvpn-server/internal/config"
	"openvpn-server/internal/server"
)

func main() {
	var configPath = flag.String("config", "configs/server.conf", "Path to server configuration file")
	var logLevel = flag.String("log-level", "info", "Log level (debug, info, warn, error)")
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set log level
	if err := setLogLevel(*logLevel); err != nil {
		log.Fatalf("Invalid log level: %v", err)
	}

	log.Printf("Starting OpenVPN server with config: %s", *configPath)
	log.Printf("Server will listen on %s:%d", cfg.ListenAddress, cfg.Port)

	// Create and start the server
	srv, err := server.New(cfg)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal, stopping server...")
		srv.Stop()
	}()

	// Start the server
	if err := srv.Start(); err != nil {
		log.Fatalf("Server failed: %v", err)
	}

	log.Println("Server stopped")
}

func setLogLevel(level string) error {
	switch level {
	case "debug":
		log.SetFlags(log.LstdFlags | log.Lshortfile)
	case "info":
		log.SetFlags(log.LstdFlags)
	case "warn", "error":
		log.SetFlags(log.LstdFlags)
	default:
		return fmt.Errorf("invalid log level: %s", level)
	}
	return nil
}
