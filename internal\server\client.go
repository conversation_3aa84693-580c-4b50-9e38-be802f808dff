package server

import (
	"crypto/rand"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"openvpn-server/internal/config"
	"openvpn-server/internal/crypto"
	"openvpn-server/internal/network"
	"openvpn-server/internal/protocol"
)

// ClientSession represents a client session
type ClientSession struct {
	ID              string
	Address         net.Addr
	State           ClientState
	SessionID       []byte
	RemoteSessionID []byte
	
	// Network
	AllocatedIP     net.IP
	ipPool          *network.IPPool
	
	// Protocol
	controlChannel  *protocol.ControlChannel
	reliableQueue   *protocol.ReliableQueue
	
	// Crypto
	tlsSession      *crypto.TLSSession
	dataCrypto      *crypto.DataChannelCrypto
	
	// Timing
	lastActivity    time.Time
	connectTime     time.Time
	
	// Statistics
	bytesReceived   uint64
	bytesSent       uint64
	packetsReceived uint64
	packetsSent     uint64
	
	// Configuration
	config          *config.Config
	
	// Synchronization
	mutex           sync.RWMutex
}

// ClientState represents the state of a client session
type ClientState int

const (
	ClientStateInit ClientState = iota
	ClientStateHandshaking
	ClientStateAuthenticating
	ClientStateConnected
	ClientStateDisconnected
)

// String returns string representation of client state
func (s ClientState) String() string {
	switch s {
	case ClientStateInit:
		return "INIT"
	case ClientStateHandshaking:
		return "HANDSHAKING"
	case ClientStateAuthenticating:
		return "AUTHENTICATING"
	case ClientStateConnected:
		return "CONNECTED"
	case ClientStateDisconnected:
		return "DISCONNECTED"
	default:
		return "UNKNOWN"
	}
}

// NewClientSession creates a new client session
func NewClientSession(id string, addr net.Addr, cfg *config.Config, tlsConfig *crypto.TLSConfig, ipPool *network.IPPool) *ClientSession {
	// Generate session ID
	sessionID := make([]byte, 8)
	rand.Read(sessionID)
	
	client := &ClientSession{
		ID:             id,
		Address:        addr,
		State:          ClientStateInit,
		SessionID:      sessionID,
		ipPool:         ipPool,
		lastActivity:   time.Now(),
		connectTime:    time.Now(),
		config:         cfg,
	}
	
	// Initialize control channel
	client.controlChannel = protocol.NewControlChannel(sessionID)
	client.reliableQueue = protocol.NewReliableQueue(5) // Max 5 retries
	
	// Initialize TLS session
	client.tlsSession = crypto.NewTLSSession(tlsConfig, true) // Server mode
	
	return client
}

// ProcessPacket processes an incoming packet from the client
func (c *ClientSession) ProcessPacket(packet *protocol.Packet) (*protocol.Packet, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.lastActivity = time.Now()
	c.packetsReceived++
	
	// Handle different packet types
	if protocol.IsControlPacket(packet.Header.Opcode) {
		return c.processControlPacket(packet)
	} else if protocol.IsDataPacket(packet.Header.Opcode) {
		return c.processDataPacket(packet)
	}
	
	return nil, fmt.Errorf("unknown packet type: %d", packet.Header.Opcode)
}

// processControlPacket processes control channel packets
func (c *ClientSession) processControlPacket(packet *protocol.Packet) (*protocol.Packet, error) {
	log.Printf("Client %s: Processing control packet %s", 
		c.ID, protocol.GetOpcodeString(packet.Header.Opcode))
	
	// Handle hard reset
	if protocol.IsResetPacket(packet.Header.Opcode) {
		c.State = ClientStateHandshaking
		c.RemoteSessionID = make([]byte, len(packet.Header.SessionID))
		copy(c.RemoteSessionID, packet.Header.SessionID)
		
		// Allocate IP address
		if c.AllocatedIP == nil {
			ip, err := c.ipPool.AllocateIP(c.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to allocate IP: %w", err)
			}
			c.AllocatedIP = ip
			log.Printf("Client %s: Allocated IP %s", c.ID, ip.String())
		}
	}
	
	// Process through control channel
	response, err := c.controlChannel.ProcessControlPacket(packet)
	if err != nil {
		return nil, fmt.Errorf("control channel error: %w", err)
	}
	
	// Update client state based on control channel state
	switch c.controlChannel.State {
	case protocol.StateTLSHandshake:
		c.State = ClientStateHandshaking
	case protocol.StateKeyExchange:
		c.State = ClientStateAuthenticating
	case protocol.StateActive:
		c.State = ClientStateConnected
		// Initialize data channel crypto if not already done
		if c.dataCrypto == nil {
			if err := c.initializeDataCrypto(); err != nil {
				log.Printf("Client %s: Failed to initialize data crypto: %v", c.ID, err)
			}
		}
	}
	
	if response != nil {
		c.packetsSent++
	}
	
	return response, nil
}

// processDataPacket processes data channel packets
func (c *ClientSession) processDataPacket(packet *protocol.Packet) (*protocol.Packet, error) {
	if c.State != ClientStateConnected {
		return nil, fmt.Errorf("client not connected")
	}
	
	if c.dataCrypto == nil {
		return nil, fmt.Errorf("data channel crypto not initialized")
	}
	
	// Decrypt the packet
	plaintext, err := c.dataCrypto.Decrypt(packet.Payload)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data packet: %w", err)
	}
	
	c.bytesReceived += uint64(len(plaintext))
	
	// TODO: Forward decrypted packet to TUN interface
	log.Printf("Client %s: Received %d bytes of data", c.ID, len(plaintext))
	
	return nil, nil
}

// SendDataPacket sends a data packet to the client
func (c *ClientSession) SendDataPacket(data []byte) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	if c.State != ClientStateConnected {
		return fmt.Errorf("client not connected")
	}
	
	if c.dataCrypto == nil {
		return fmt.Errorf("data channel crypto not initialized")
	}
	
	// Encrypt the data
	ciphertext, err := c.dataCrypto.Encrypt(data)
	if err != nil {
		return fmt.Errorf("failed to encrypt data: %w", err)
	}
	
	// Create data packet
	packet := protocol.NewDataPacket(0, c.SessionID, ciphertext)
	
	// TODO: Send packet to client via UDP socket
	c.bytesSent += uint64(len(data))
	c.packetsSent++
	
	log.Printf("Client %s: Sent %d bytes of data", c.ID, len(data))
	
	return nil
}

// initializeDataCrypto initializes data channel encryption
func (c *ClientSession) initializeDataCrypto() error {
	// Get key material from TLS session
	keyMaterial := c.tlsSession.GetKeyMaterial()
	if len(keyMaterial) == 0 {
		// Generate mock key material for testing
		keyMaterial = make([]byte, 128)
		rand.Read(keyMaterial)
	}
	
	// Initialize data channel crypto
	var err error
	c.dataCrypto, err = crypto.NewDataChannelCrypto(c.config.Cipher, keyMaterial)
	if err != nil {
		return fmt.Errorf("failed to create data channel crypto: %w", err)
	}
	
	log.Printf("Client %s: Data channel crypto initialized with %s", c.ID, c.config.Cipher)
	return nil
}

// IsConnected returns true if the client is connected
func (c *ClientSession) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.State == ClientStateConnected
}

// GetAllocatedIP returns the IP address allocated to this client
func (c *ClientSession) GetAllocatedIP() net.IP {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.AllocatedIP
}

// GetStats returns client statistics
func (c *ClientSession) GetStats() ClientStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return ClientStats{
		ID:              c.ID,
		Address:         c.Address.String(),
		State:           c.State.String(),
		AllocatedIP:     c.AllocatedIP.String(),
		ConnectTime:     c.connectTime,
		LastActivity:    c.lastActivity,
		BytesReceived:   c.bytesReceived,
		BytesSent:       c.bytesSent,
		PacketsReceived: c.packetsReceived,
		PacketsSent:     c.packetsSent,
	}
}

// IsTimedOut returns true if the client has timed out
func (c *ClientSession) IsTimedOut() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	timeout := c.config.ClientTimeout
	if timeout == 0 {
		timeout = 60 * time.Second // Default timeout
	}
	
	return time.Since(c.lastActivity) > timeout
}

// Close closes the client session
func (c *ClientSession) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	if c.State == ClientStateDisconnected {
		return nil
	}
	
	log.Printf("Client %s: Closing session", c.ID)
	
	// Release allocated IP
	if c.AllocatedIP != nil {
		c.ipPool.ReleaseIP(c.ID)
	}
	
	// Close TLS session
	if c.tlsSession != nil {
		c.tlsSession.Close()
	}
	
	c.State = ClientStateDisconnected
	
	return nil
}

// ClientStats represents client statistics
type ClientStats struct {
	ID              string    `json:"id"`
	Address         string    `json:"address"`
	State           string    `json:"state"`
	AllocatedIP     string    `json:"allocated_ip"`
	ConnectTime     time.Time `json:"connect_time"`
	LastActivity    time.Time `json:"last_activity"`
	BytesReceived   uint64    `json:"bytes_received"`
	BytesSent       uint64    `json:"bytes_sent"`
	PacketsReceived uint64    `json:"packets_received"`
	PacketsSent     uint64    `json:"packets_sent"`
}
