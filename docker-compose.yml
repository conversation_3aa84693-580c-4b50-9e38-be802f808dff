version: '3.8'

services:
  openvpn-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: openvpn-server
    restart: unless-stopped
    
    # Network configuration
    ports:
      - "1194:1194/udp"
    
    # Privileged mode required for TUN/TAP interface creation
    privileged: true
    
    # Additional capabilities
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    
    # Device access
    devices:
      - /dev/net/tun:/dev/net/tun
    
    # Volume mounts
    volumes:
      - ./configs:/app/configs:ro
      - ./certs:/app/certs:ro
      - openvpn-logs:/var/log/openvpn
      - /lib/modules:/lib/modules:ro
    
    # Environment variables
    environment:
      - OPENVPN_CONFIG=/app/configs/server.conf
      - OPENVPN_LOG_LEVEL=info
    
    # Command override (optional)
    # command: ["./openvpn-server", "-config", "/app/configs/server.conf", "-log-level", "debug"]
    
    # Health check
    healthcheck:
      test: ["CMD", "pgrep", "openvpn-server"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  # Optional: Web management interface
  openvpn-admin:
    image: nginx:alpine
    container_name: openvpn-admin
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./web:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - openvpn-server
    profiles:
      - admin

  # Optional: Log aggregation
  log-aggregator:
    image: fluent/fluent-bit:latest
    container_name: openvpn-logs
    restart: unless-stopped
    volumes:
      - openvpn-logs:/var/log/openvpn:ro
      - ./fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
    depends_on:
      - openvpn-server
    profiles:
      - logging

  # Optional: Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: openvpn-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: openvpn-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    profiles:
      - monitoring

# Named volumes
volumes:
  openvpn-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# Networks (optional custom network)
networks:
  default:
    name: openvpn-network
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
