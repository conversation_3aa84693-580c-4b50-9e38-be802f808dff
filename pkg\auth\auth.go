package auth

import (
	"crypto/x509"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// AuthMethod represents different authentication methods
type AuthMethod int

const (
	AuthMethodCertificate AuthMethod = iota
	AuthMethodUserPass
	AuthMethodCombined // Certificate + Username/Password
)

// AuthR<PERSON><PERSON> represents the result of authentication
type AuthResult struct {
	Success     bool
	Username    string
	CommonName  string
	ErrorMsg    string
	Attributes  map[string]string
}

// Authenticator interface for different authentication methods
type Authenticator interface {
	Authenticate(credentials *Credentials) (*AuthResult, error)
	GetMethod() AuthMethod
}

// Credentials holds authentication credentials
type Credentials struct {
	// Certificate-based auth
	ClientCert *x509.Certificate
	CACert     *x509.Certificate
	
	// Username/password auth
	Username string
	Password string
	
	// Additional context
	ClientIP   string
	RemotePort int
	Timestamp  time.Time
}

// CertificateAuthenticator handles certificate-based authentication
type CertificateAuthenticator struct {
	caCert          *x509.Certificate
	allowDuplicateCN bool
	revokedCerts    map[string]bool // Serial numbers of revoked certificates
}

// NewCertificateAuthenticator creates a new certificate authenticator
func NewCertificateAuthenticator(caCert *x509.Certificate, allowDuplicateCN bool) *CertificateAuthenticator {
	return &CertificateAuthenticator{
		caCert:          caCert,
		allowDuplicateCN: allowDuplicateCN,
		revokedCerts:    make(map[string]bool),
	}
}

// Authenticate performs certificate-based authentication
func (ca *CertificateAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	if creds.ClientCert == nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "no client certificate provided",
		}, nil
	}
	
	// Verify certificate chain
	roots := x509.NewCertPool()
	roots.AddCert(ca.caCert)
	
	opts := x509.VerifyOptions{
		Roots:     roots,
		KeyUsages: []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth},
	}
	
	_, err := creds.ClientCert.Verify(opts)
	if err != nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: fmt.Sprintf("certificate verification failed: %v", err),
		}, nil
	}
	
	// Check if certificate is revoked
	serialNumber := creds.ClientCert.SerialNumber.String()
	if ca.revokedCerts[serialNumber] {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "certificate has been revoked",
		}, nil
	}
	
	// Check certificate validity period
	now := time.Now()
	if now.Before(creds.ClientCert.NotBefore) || now.After(creds.ClientCert.NotAfter) {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "certificate is not valid at current time",
		}, nil
	}
	
	// Extract common name
	commonName := creds.ClientCert.Subject.CommonName
	if commonName == "" {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "certificate has no common name",
		}, nil
	}
	
	return &AuthResult{
		Success:    true,
		CommonName: commonName,
		Attributes: map[string]string{
			"serial_number": serialNumber,
			"issuer":        creds.ClientCert.Issuer.String(),
			"subject":       creds.ClientCert.Subject.String(),
		},
	}, nil
}

// GetMethod returns the authentication method
func (ca *CertificateAuthenticator) GetMethod() AuthMethod {
	return AuthMethodCertificate
}

// RevokeCertificate adds a certificate to the revocation list
func (ca *CertificateAuthenticator) RevokeCertificate(serialNumber string) {
	ca.revokedCerts[serialNumber] = true
}

// UserPassAuthenticator handles username/password authentication
type UserPassAuthenticator struct {
	authScript string
	users      map[string]string // username -> password hash (for simple auth)
}

// NewUserPassAuthenticator creates a new username/password authenticator
func NewUserPassAuthenticator(authScript string) *UserPassAuthenticator {
	return &UserPassAuthenticator{
		authScript: authScript,
		users:      make(map[string]string),
	}
}

// Authenticate performs username/password authentication
func (upa *UserPassAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	if creds.Username == "" || creds.Password == "" {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "username and password required",
		}, nil
	}
	
	// If auth script is configured, use it
	if upa.authScript != "" {
		return upa.authenticateWithScript(creds)
	}
	
	// Otherwise use simple user database
	return upa.authenticateWithUserDB(creds)
}

// authenticateWithScript uses an external script for authentication
func (upa *UserPassAuthenticator) authenticateWithScript(creds *Credentials) (*AuthResult, error) {
	// Prepare environment variables for the script
	env := []string{
		fmt.Sprintf("username=%s", creds.Username),
		fmt.Sprintf("password=%s", creds.Password),
		fmt.Sprintf("trusted_ip=%s", creds.ClientIP),
		fmt.Sprintf("trusted_port=%d", creds.RemotePort),
	}
	
	// Execute the authentication script
	cmd := exec.Command(upa.authScript)
	cmd.Env = env
	
	err := cmd.Run()
	if err != nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "authentication script failed",
		}, nil
	}
	
	// If script exits with code 0, authentication succeeded
	return &AuthResult{
		Success:  true,
		Username: creds.Username,
	}, nil
}

// authenticateWithUserDB uses simple user database for authentication
func (upa *UserPassAuthenticator) authenticateWithUserDB(creds *Credentials) (*AuthResult, error) {
	// This is a simplified implementation
	// In production, passwords should be properly hashed
	
	expectedPassword, exists := upa.users[creds.Username]
	if !exists {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "user not found",
		}, nil
	}
	
	if expectedPassword != creds.Password {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "invalid password",
		}, nil
	}
	
	return &AuthResult{
		Success:  true,
		Username: creds.Username,
	}, nil
}

// GetMethod returns the authentication method
func (upa *UserPassAuthenticator) GetMethod() AuthMethod {
	return AuthMethodUserPass
}

// AddUser adds a user to the simple user database
func (upa *UserPassAuthenticator) AddUser(username, password string) {
	upa.users[username] = password
}

// CombinedAuthenticator handles both certificate and username/password authentication
type CombinedAuthenticator struct {
	certAuth     *CertificateAuthenticator
	userPassAuth *UserPassAuthenticator
}

// NewCombinedAuthenticator creates a new combined authenticator
func NewCombinedAuthenticator(certAuth *CertificateAuthenticator, userPassAuth *UserPassAuthenticator) *CombinedAuthenticator {
	return &CombinedAuthenticator{
		certAuth:     certAuth,
		userPassAuth: userPassAuth,
	}
}

// Authenticate performs combined authentication
func (ca *CombinedAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	// First, authenticate certificate
	certResult, err := ca.certAuth.Authenticate(creds)
	if err != nil {
		return nil, err
	}
	
	if !certResult.Success {
		return certResult, nil
	}
	
	// Then, authenticate username/password
	userPassResult, err := ca.userPassAuth.Authenticate(creds)
	if err != nil {
		return nil, err
	}
	
	if !userPassResult.Success {
		return userPassResult, nil
	}
	
	// Combine results
	result := &AuthResult{
		Success:    true,
		Username:   userPassResult.Username,
		CommonName: certResult.CommonName,
		Attributes: make(map[string]string),
	}
	
	// Merge attributes
	for k, v := range certResult.Attributes {
		result.Attributes[k] = v
	}
	for k, v := range userPassResult.Attributes {
		result.Attributes[k] = v
	}
	
	return result, nil
}

// GetMethod returns the authentication method
func (ca *CombinedAuthenticator) GetMethod() AuthMethod {
	return AuthMethodCombined
}

// AuthManager manages authentication for the OpenVPN server
type AuthManager struct {
	authenticators []Authenticator
	authLog        []AuthLogEntry
}

// AuthLogEntry represents an authentication log entry
type AuthLogEntry struct {
	Timestamp time.Time
	ClientIP  string
	Username  string
	Success   bool
	Method    AuthMethod
	ErrorMsg  string
}

// NewAuthManager creates a new authentication manager
func NewAuthManager() *AuthManager {
	return &AuthManager{
		authenticators: make([]Authenticator, 0),
		authLog:        make([]AuthLogEntry, 0),
	}
}

// AddAuthenticator adds an authenticator to the manager
func (am *AuthManager) AddAuthenticator(auth Authenticator) {
	am.authenticators = append(am.authenticators, auth)
}

// Authenticate performs authentication using configured authenticators
func (am *AuthManager) Authenticate(creds *Credentials) (*AuthResult, error) {
	if len(am.authenticators) == 0 {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "no authenticators configured",
		}, nil
	}
	
	// Try each authenticator in order
	var lastResult *AuthResult
	for _, auth := range am.authenticators {
		result, err := auth.Authenticate(creds)
		if err != nil {
			return nil, err
		}
		
		lastResult = result
		
		// Log authentication attempt
		am.logAuthAttempt(creds, result, auth.GetMethod())
		
		if result.Success {
			return result, nil
		}
	}
	
	return lastResult, nil
}

// logAuthAttempt logs an authentication attempt
func (am *AuthManager) logAuthAttempt(creds *Credentials, result *AuthResult, method AuthMethod) {
	entry := AuthLogEntry{
		Timestamp: time.Now(),
		ClientIP:  creds.ClientIP,
		Username:  creds.Username,
		Success:   result.Success,
		Method:    method,
		ErrorMsg:  result.ErrorMsg,
	}
	
	am.authLog = append(am.authLog, entry)
	
	// Keep only last 1000 entries
	if len(am.authLog) > 1000 {
		am.authLog = am.authLog[len(am.authLog)-1000:]
	}
}

// GetAuthLog returns the authentication log
func (am *AuthManager) GetAuthLog() []AuthLogEntry {
	return am.authLog
}

// GetAuthStats returns authentication statistics
func (am *AuthManager) GetAuthStats() AuthStats {
	stats := AuthStats{
		TotalAttempts:    len(am.authLog),
		SuccessfulAuths:  0,
		FailedAuths:      0,
		MethodStats:      make(map[string]int),
	}
	
	for _, entry := range am.authLog {
		if entry.Success {
			stats.SuccessfulAuths++
		} else {
			stats.FailedAuths++
		}
		
		methodName := getMethodName(entry.Method)
		stats.MethodStats[methodName]++
	}
	
	return stats
}

// AuthStats represents authentication statistics
type AuthStats struct {
	TotalAttempts   int            `json:"total_attempts"`
	SuccessfulAuths int            `json:"successful_auths"`
	FailedAuths     int            `json:"failed_auths"`
	MethodStats     map[string]int `json:"method_stats"`
}

// getMethodName returns the string name of an authentication method
func getMethodName(method AuthMethod) string {
	switch method {
	case AuthMethodCertificate:
		return "certificate"
	case AuthMethodUserPass:
		return "userpass"
	case AuthMethodCombined:
		return "combined"
	default:
		return "unknown"
	}
}
