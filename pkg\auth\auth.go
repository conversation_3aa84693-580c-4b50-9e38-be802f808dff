package auth

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"crypto/x509"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	"os/exec"
	"time"
)

// AuthMethod represents different authentication methods
type AuthMethod int

const (
	AuthMethodCertificate AuthMethod = iota
	AuthMethodUserPass
	AuthMethodCombined // Certificate + Username/Password
)

// AuthResult represents the result of authentication
type AuthResult struct {
	Success    bool
	Username   string
	CommonName string
	ErrorMsg   string
	Attributes map[string]string
}

// Authenticator interface for different authentication methods
type Authenticator interface {
	Authenticate(credentials *Credentials) (*AuthResult, error)
	GetMethod() AuthMethod
}

// Credentials holds authentication credentials
type Credentials struct {
	// Certificate-based auth
	ClientCert *x509.Certificate
	CACert     *x509.Certificate

	// Username/password auth
	Username string
	Password string

	// Additional context
	ClientIP   string
	RemotePort int
	Timestamp  time.Time
}

// UserRecord represents a user account with security features
type UserRecord struct {
	Username     string
	PasswordHash string
	Salt         string
	Created      time.Time
	LastLogin    time.Time
	Enabled      bool
	Roles        []string
	Attributes   map[string]string
}

// FailureRecord tracks authentication failures for rate limiting
type FailureRecord struct {
	Count       int
	LastAttempt time.Time
	LockedUntil time.Time
}

// CertificateAuthenticator handles certificate-based authentication
type CertificateAuthenticator struct {
	caCert           *x509.Certificate
	allowDuplicateCN bool
	revokedCerts     map[string]bool // Serial numbers of revoked certificates
	crlURL           string          // Certificate Revocation List URL
	ocspURL          string          // OCSP responder URL
	httpClient       *http.Client    // HTTP client for CRL/OCSP requests
}

// NewCertificateAuthenticator creates a new certificate authenticator
func NewCertificateAuthenticator(caCert *x509.Certificate, allowDuplicateCN bool) *CertificateAuthenticator {
	return &CertificateAuthenticator{
		caCert:           caCert,
		allowDuplicateCN: allowDuplicateCN,
		revokedCerts:     make(map[string]bool),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SetCRLURL sets the Certificate Revocation List URL
func (ca *CertificateAuthenticator) SetCRLURL(url string) {
	ca.crlURL = url
}

// SetOCSPURL sets the OCSP responder URL
func (ca *CertificateAuthenticator) SetOCSPURL(url string) {
	ca.ocspURL = url
}

// Authenticate performs certificate-based authentication with comprehensive validation
func (ca *CertificateAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	if creds.ClientCert == nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "no client certificate provided",
		}, nil
	}

	// Perform basic certificate validation
	if err := ca.validateCertificateBasics(creds.ClientCert); err != nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: fmt.Sprintf("certificate validation failed: %v", err),
		}, nil
	}

	// Verify certificate chain
	if err := ca.verifyCertificateChain(creds.ClientCert); err != nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: fmt.Sprintf("certificate chain verification failed: %v", err),
		}, nil
	}

	// Check revocation status
	serialNumber := creds.ClientCert.SerialNumber.String()
	if revoked, err := ca.checkRevocationStatus(creds.ClientCert); err != nil {
		log.Printf("Warning: revocation check failed: %v", err)
	} else if revoked {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "certificate has been revoked",
		}, nil
	}

	// Extract and validate common name
	commonName := creds.ClientCert.Subject.CommonName
	if commonName == "" {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "certificate has no common name",
		}, nil
	}

	// Check for duplicate CN if not allowed
	if !ca.allowDuplicateCN {
		// In a real implementation, this would check against active sessions
		log.Printf("Certificate CN validation: %s", commonName)
	}

	// Create successful result with detailed attributes
	attributes := map[string]string{
		"serial_number":   serialNumber,
		"issuer":          creds.ClientCert.Issuer.String(),
		"subject":         creds.ClientCert.Subject.String(),
		"not_before":      creds.ClientCert.NotBefore.Format(time.RFC3339),
		"not_after":       creds.ClientCert.NotAfter.Format(time.RFC3339),
		"signature_algo":  creds.ClientCert.SignatureAlgorithm.String(),
		"public_key_algo": creds.ClientCert.PublicKeyAlgorithm.String(),
	}

	// Add subject alternative names if present
	if len(creds.ClientCert.DNSNames) > 0 {
		attributes["dns_names"] = fmt.Sprintf("%v", creds.ClientCert.DNSNames)
	}
	if len(creds.ClientCert.EmailAddresses) > 0 {
		attributes["email_addresses"] = fmt.Sprintf("%v", creds.ClientCert.EmailAddresses)
	}

	return &AuthResult{
		Success:    true,
		CommonName: commonName,
		Attributes: attributes,
	}, nil
}

// validateCertificateBasics performs basic certificate validation
func (ca *CertificateAuthenticator) validateCertificateBasics(cert *x509.Certificate) error {
	// Check certificate validity period
	now := time.Now()
	if now.Before(cert.NotBefore) {
		return fmt.Errorf("certificate not yet valid (valid from %v)", cert.NotBefore)
	}
	if now.After(cert.NotAfter) {
		return fmt.Errorf("certificate has expired (expired on %v)", cert.NotAfter)
	}

	// Check key usage
	if cert.KeyUsage&x509.KeyUsageDigitalSignature == 0 {
		return fmt.Errorf("certificate does not have digital signature key usage")
	}

	// Check extended key usage for client authentication
	hasClientAuth := false
	for _, usage := range cert.ExtKeyUsage {
		if usage == x509.ExtKeyUsageClientAuth {
			hasClientAuth = true
			break
		}
	}
	if !hasClientAuth {
		return fmt.Errorf("certificate does not have client authentication extended key usage")
	}

	return nil
}

// verifyCertificateChain verifies the certificate chain
func (ca *CertificateAuthenticator) verifyCertificateChain(cert *x509.Certificate) error {
	roots := x509.NewCertPool()
	roots.AddCert(ca.caCert)

	opts := x509.VerifyOptions{
		Roots:       roots,
		KeyUsages:   []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth},
		CurrentTime: time.Now(),
	}

	chains, err := cert.Verify(opts)
	if err != nil {
		return fmt.Errorf("certificate chain verification failed: %w", err)
	}

	if len(chains) == 0 {
		return fmt.Errorf("no valid certificate chains found")
	}

	// Log the verified chain for audit purposes
	log.Printf("Certificate chain verified: %d chains found", len(chains))
	for i, chain := range chains {
		log.Printf("Chain %d: %d certificates", i, len(chain))
		for j, chainCert := range chain {
			log.Printf("  [%d] Subject: %s", j, chainCert.Subject.String())
		}
	}

	return nil
}

// checkRevocationStatus checks if the certificate is revoked
func (ca *CertificateAuthenticator) checkRevocationStatus(cert *x509.Certificate) (bool, error) {
	serialNumber := cert.SerialNumber.String()

	// Check local revocation list first
	if ca.revokedCerts[serialNumber] {
		return true, nil
	}

	// Check CRL if URL is configured
	if ca.crlURL != "" {
		if revoked, err := ca.checkCRL(cert); err != nil {
			return false, fmt.Errorf("CRL check failed: %w", err)
		} else if revoked {
			// Cache the revocation status
			ca.revokedCerts[serialNumber] = true
			return true, nil
		}
	}

	// Check OCSP if URL is configured
	if ca.ocspURL != "" {
		if revoked, err := ca.checkOCSP(cert); err != nil {
			return false, fmt.Errorf("OCSP check failed: %w", err)
		} else if revoked {
			// Cache the revocation status
			ca.revokedCerts[serialNumber] = true
			return true, nil
		}
	}

	return false, nil
}

// checkCRL checks certificate revocation list
func (ca *CertificateAuthenticator) checkCRL(cert *x509.Certificate) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", ca.crlURL, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create CRL request: %w", err)
	}

	resp, err := ca.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to fetch CRL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false, fmt.Errorf("CRL request failed with status: %d", resp.StatusCode)
	}

	// In a real implementation, we would parse the CRL and check the certificate
	// For now, we'll just log that we attempted the check
	log.Printf("CRL check performed for certificate serial: %s", cert.SerialNumber.String())

	return false, nil
}

// checkOCSP checks certificate status via OCSP
func (ca *CertificateAuthenticator) checkOCSP(cert *x509.Certificate) (bool, error) {
	// In a real implementation, we would:
	// 1. Create an OCSP request
	// 2. Send it to the OCSP responder
	// 3. Parse the response
	// 4. Check the certificate status

	log.Printf("OCSP check performed for certificate serial: %s", cert.SerialNumber.String())

	return false, nil
}

// GetMethod returns the authentication method
func (ca *CertificateAuthenticator) GetMethod() AuthMethod {
	return AuthMethodCertificate
}

// RevokeCertificate adds a certificate to the revocation list
func (ca *CertificateAuthenticator) RevokeCertificate(serialNumber string) {
	ca.revokedCerts[serialNumber] = true
}

// UserPassAuthenticator handles username/password authentication
type UserPassAuthenticator struct {
	authScript     string
	users          map[string]*UserRecord    // username -> user record
	maxAttempts    int                       // Maximum failed attempts before lockout
	lockoutTime    time.Duration             // Lockout duration
	failedAttempts map[string]*FailureRecord // Track failed attempts
}

// NewUserPassAuthenticator creates a new username/password authenticator
func NewUserPassAuthenticator(authScript string) *UserPassAuthenticator {
	return &UserPassAuthenticator{
		authScript:     authScript,
		users:          make(map[string]*UserRecord),
		maxAttempts:    5,
		lockoutTime:    15 * time.Minute,
		failedAttempts: make(map[string]*FailureRecord),
	}
}

// Authenticate performs username/password authentication with security features
func (upa *UserPassAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	if creds.Username == "" || creds.Password == "" {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "username and password required",
		}, nil
	}

	// Check if user is locked out
	if locked, until := upa.isLockedOut(creds.Username); locked {
		return &AuthResult{
			Success:  false,
			ErrorMsg: fmt.Sprintf("account locked until %v", until.Format(time.RFC3339)),
		}, nil
	}

	var result *AuthResult
	var err error

	// If auth script is configured, use it
	if upa.authScript != "" {
		result, err = upa.authenticateWithScript(creds)
	} else {
		// Otherwise use user database
		result, err = upa.authenticateWithUserDB(creds)
	}

	// Handle failed authentication
	if err != nil || !result.Success {
		upa.recordFailedAttempt(creds.Username)
		return result, err
	}

	// Clear failed attempts on successful authentication
	upa.clearFailedAttempts(creds.Username)

	// Update last login time if using user database
	if upa.authScript == "" {
		if user, exists := upa.users[creds.Username]; exists {
			user.LastLogin = time.Now()
		}
	}

	return result, nil
}

// authenticateWithScript uses an external script for authentication
func (upa *UserPassAuthenticator) authenticateWithScript(creds *Credentials) (*AuthResult, error) {
	// Prepare environment variables for the script
	env := []string{
		fmt.Sprintf("username=%s", creds.Username),
		fmt.Sprintf("password=%s", creds.Password),
		fmt.Sprintf("trusted_ip=%s", creds.ClientIP),
		fmt.Sprintf("trusted_port=%d", creds.RemotePort),
	}

	// Execute the authentication script
	cmd := exec.Command(upa.authScript)
	cmd.Env = env

	err := cmd.Run()
	if err != nil {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "authentication script failed",
		}, nil
	}

	// If script exits with code 0, authentication succeeded
	return &AuthResult{
		Success:  true,
		Username: creds.Username,
	}, nil
}

// authenticateWithUserDB uses user database for authentication with bcrypt
func (upa *UserPassAuthenticator) authenticateWithUserDB(creds *Credentials) (*AuthResult, error) {
	user, exists := upa.users[creds.Username]
	if !exists {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "user not found",
		}, nil
	}

	if !user.Enabled {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "account disabled",
		}, nil
	}

	// Verify password using secure hash comparison
	if !verifyPassword(creds.Password, user.PasswordHash, user.Salt) {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "invalid password",
		}, nil
	}

	// Create successful result with user attributes
	attributes := make(map[string]string)
	for k, v := range user.Attributes {
		attributes[k] = v
	}
	attributes["last_login"] = user.LastLogin.Format(time.RFC3339)
	attributes["roles"] = fmt.Sprintf("%v", user.Roles)

	return &AuthResult{
		Success:    true,
		Username:   creds.Username,
		Attributes: attributes,
	}, nil
}

// GetMethod returns the authentication method
func (upa *UserPassAuthenticator) GetMethod() AuthMethod {
	return AuthMethodUserPass
}

// AddUser adds a user to the user database with secure password hashing
func (upa *UserPassAuthenticator) AddUser(username, password string) error {
	// Generate salt and hash password
	salt, hashedPassword, err := hashPassword(password)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user record
	user := &UserRecord{
		Username:     username,
		PasswordHash: hashedPassword,
		Salt:         salt,
		Created:      time.Now(),
		LastLogin:    time.Time{}, // Zero time
		Enabled:      true,
		Roles:        []string{"user"},
		Attributes:   make(map[string]string),
	}

	upa.users[username] = user
	return nil
}

// hashPassword creates a secure hash of the password with salt
func hashPassword(password string) (string, string, error) {
	// Generate random salt
	salt := make([]byte, 32)
	if _, err := rand.Read(salt); err != nil {
		return "", "", fmt.Errorf("failed to generate salt: %w", err)
	}

	saltHex := hex.EncodeToString(salt)

	// Hash password with salt using PBKDF2-like approach
	hash := sha256.New()
	hash.Write([]byte(password))
	hash.Write(salt)

	// Multiple rounds for security
	hashedBytes := hash.Sum(nil)
	for i := 0; i < 10000; i++ {
		hash.Reset()
		hash.Write(hashedBytes)
		hash.Write(salt)
		hashedBytes = hash.Sum(nil)
	}

	hashedPassword := hex.EncodeToString(hashedBytes)

	return saltHex, hashedPassword, nil
}

// verifyPassword verifies a password against its hash and salt
func verifyPassword(password, hashedPassword, saltHex string) bool {
	// Decode salt
	salt, err := hex.DecodeString(saltHex)
	if err != nil {
		return false
	}

	// Hash the provided password with the same salt
	hash := sha256.New()
	hash.Write([]byte(password))
	hash.Write(salt)

	// Multiple rounds for security
	hashedBytes := hash.Sum(nil)
	for i := 0; i < 10000; i++ {
		hash.Reset()
		hash.Write(hashedBytes)
		hash.Write(salt)
		hashedBytes = hash.Sum(nil)
	}

	computedHash := hex.EncodeToString(hashedBytes)

	// Use constant-time comparison to prevent timing attacks
	return subtle.ConstantTimeCompare([]byte(computedHash), []byte(hashedPassword)) == 1
}

// isLockedOut checks if a user is currently locked out
func (upa *UserPassAuthenticator) isLockedOut(username string) (bool, time.Time) {
	failure, exists := upa.failedAttempts[username]
	if !exists {
		return false, time.Time{}
	}

	if failure.Count >= upa.maxAttempts && time.Now().Before(failure.LockedUntil) {
		return true, failure.LockedUntil
	}

	return false, time.Time{}
}

// recordFailedAttempt records a failed authentication attempt
func (upa *UserPassAuthenticator) recordFailedAttempt(username string) {
	now := time.Now()

	failure, exists := upa.failedAttempts[username]
	if !exists {
		failure = &FailureRecord{
			Count:       0,
			LastAttempt: now,
		}
		upa.failedAttempts[username] = failure
	}

	failure.Count++
	failure.LastAttempt = now

	// Set lockout time if max attempts reached
	if failure.Count >= upa.maxAttempts {
		failure.LockedUntil = now.Add(upa.lockoutTime)
		log.Printf("User %s locked out until %v after %d failed attempts",
			username, failure.LockedUntil, failure.Count)
	}
}

// clearFailedAttempts clears failed attempt records for a user
func (upa *UserPassAuthenticator) clearFailedAttempts(username string) {
	delete(upa.failedAttempts, username)
}

// SetMaxAttempts sets the maximum failed attempts before lockout
func (upa *UserPassAuthenticator) SetMaxAttempts(max int) {
	upa.maxAttempts = max
}

// SetLockoutTime sets the lockout duration
func (upa *UserPassAuthenticator) SetLockoutTime(duration time.Duration) {
	upa.lockoutTime = duration
}

// CombinedAuthenticator handles both certificate and username/password authentication
type CombinedAuthenticator struct {
	certAuth     *CertificateAuthenticator
	userPassAuth *UserPassAuthenticator
}

// NewCombinedAuthenticator creates a new combined authenticator
func NewCombinedAuthenticator(certAuth *CertificateAuthenticator, userPassAuth *UserPassAuthenticator) *CombinedAuthenticator {
	return &CombinedAuthenticator{
		certAuth:     certAuth,
		userPassAuth: userPassAuth,
	}
}

// Authenticate performs combined authentication
func (ca *CombinedAuthenticator) Authenticate(creds *Credentials) (*AuthResult, error) {
	// First, authenticate certificate
	certResult, err := ca.certAuth.Authenticate(creds)
	if err != nil {
		return nil, err
	}

	if !certResult.Success {
		return certResult, nil
	}

	// Then, authenticate username/password
	userPassResult, err := ca.userPassAuth.Authenticate(creds)
	if err != nil {
		return nil, err
	}

	if !userPassResult.Success {
		return userPassResult, nil
	}

	// Combine results
	result := &AuthResult{
		Success:    true,
		Username:   userPassResult.Username,
		CommonName: certResult.CommonName,
		Attributes: make(map[string]string),
	}

	// Merge attributes
	for k, v := range certResult.Attributes {
		result.Attributes[k] = v
	}
	for k, v := range userPassResult.Attributes {
		result.Attributes[k] = v
	}

	return result, nil
}

// GetMethod returns the authentication method
func (ca *CombinedAuthenticator) GetMethod() AuthMethod {
	return AuthMethodCombined
}

// AuthManager manages authentication for the OpenVPN server
type AuthManager struct {
	authenticators []Authenticator
	authLog        []AuthLogEntry
}

// AuthLogEntry represents an authentication log entry
type AuthLogEntry struct {
	Timestamp time.Time
	ClientIP  string
	Username  string
	Success   bool
	Method    AuthMethod
	ErrorMsg  string
}

// NewAuthManager creates a new authentication manager
func NewAuthManager() *AuthManager {
	return &AuthManager{
		authenticators: make([]Authenticator, 0),
		authLog:        make([]AuthLogEntry, 0),
	}
}

// AddAuthenticator adds an authenticator to the manager
func (am *AuthManager) AddAuthenticator(auth Authenticator) {
	am.authenticators = append(am.authenticators, auth)
}

// Authenticate performs authentication using configured authenticators
func (am *AuthManager) Authenticate(creds *Credentials) (*AuthResult, error) {
	if len(am.authenticators) == 0 {
		return &AuthResult{
			Success:  false,
			ErrorMsg: "no authenticators configured",
		}, nil
	}

	// Try each authenticator in order
	var lastResult *AuthResult
	for _, auth := range am.authenticators {
		result, err := auth.Authenticate(creds)
		if err != nil {
			return nil, err
		}

		lastResult = result

		// Log authentication attempt
		am.logAuthAttempt(creds, result, auth.GetMethod())

		if result.Success {
			return result, nil
		}
	}

	return lastResult, nil
}

// logAuthAttempt logs an authentication attempt
func (am *AuthManager) logAuthAttempt(creds *Credentials, result *AuthResult, method AuthMethod) {
	entry := AuthLogEntry{
		Timestamp: time.Now(),
		ClientIP:  creds.ClientIP,
		Username:  creds.Username,
		Success:   result.Success,
		Method:    method,
		ErrorMsg:  result.ErrorMsg,
	}

	am.authLog = append(am.authLog, entry)

	// Keep only last 1000 entries
	if len(am.authLog) > 1000 {
		am.authLog = am.authLog[len(am.authLog)-1000:]
	}
}

// GetAuthLog returns the authentication log
func (am *AuthManager) GetAuthLog() []AuthLogEntry {
	return am.authLog
}

// GetAuthStats returns authentication statistics
func (am *AuthManager) GetAuthStats() AuthStats {
	stats := AuthStats{
		TotalAttempts:   len(am.authLog),
		SuccessfulAuths: 0,
		FailedAuths:     0,
		MethodStats:     make(map[string]int),
	}

	for _, entry := range am.authLog {
		if entry.Success {
			stats.SuccessfulAuths++
		} else {
			stats.FailedAuths++
		}

		methodName := getMethodName(entry.Method)
		stats.MethodStats[methodName]++
	}

	return stats
}

// AuthStats represents authentication statistics
type AuthStats struct {
	TotalAttempts   int            `json:"total_attempts"`
	SuccessfulAuths int            `json:"successful_auths"`
	FailedAuths     int            `json:"failed_auths"`
	MethodStats     map[string]int `json:"method_stats"`
}

// getMethodName returns the string name of an authentication method
func getMethodName(method AuthMethod) string {
	switch method {
	case AuthMethodCertificate:
		return "certificate"
	case AuthMethodUserPass:
		return "userpass"
	case AuthMethodCombined:
		return "combined"
	default:
		return "unknown"
	}
}
