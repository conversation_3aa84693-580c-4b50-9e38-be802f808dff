package crypto

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"net"
	"time"
)

// TLSConfig holds TLS configuration for OpenVPN
type TLSConfig struct {
	CACert     *x509.Certificate
	ServerCert *x509.Certificate
	ServerKey  *rsa.PrivateKey
	TLSAuth    []byte
	Config     *tls.Config
}

// LoadTLSConfig loads TLS configuration from files
func LoadTLSConfig(caCertFile, serverCertFile, serverKeyFile, tlsAuthFile string) (*TLSConfig, error) {
	tlsConfig := &TLSConfig{}

	// Load CA certificate
	caCertPEM, err := ioutil.ReadFile(caCertFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read CA certificate: %w", err)
	}

	caCertBlock, _ := pem.Decode(caCertPEM)
	if caCertBlock == nil {
		return nil, fmt.Errorf("failed to decode CA certificate PEM")
	}

	tlsConfig.CACert, err = x509.ParseCertificate(caCertBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CA certificate: %w", err)
	}

	// Load server certificate
	serverCertPEM, err := ioutil.ReadFile(serverCertFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read server certificate: %w", err)
	}

	serverCertBlock, _ := pem.Decode(serverCertPEM)
	if serverCertBlock == nil {
		return nil, fmt.Errorf("failed to decode server certificate PEM")
	}

	tlsConfig.ServerCert, err = x509.ParseCertificate(serverCertBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse server certificate: %w", err)
	}

	// Load server private key
	serverKeyPEM, err := ioutil.ReadFile(serverKeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read server key: %w", err)
	}

	serverKeyBlock, _ := pem.Decode(serverKeyPEM)
	if serverKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode server key PEM")
	}

	serverKey, err := x509.ParsePKCS1PrivateKey(serverKeyBlock.Bytes)
	if err != nil {
		// Try PKCS8 format
		keyInterface, err := x509.ParsePKCS8PrivateKey(serverKeyBlock.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse server key: %w", err)
		}
		var ok bool
		serverKey, ok = keyInterface.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("server key is not RSA private key")
		}
	}
	tlsConfig.ServerKey = serverKey

	// Load TLS auth key (optional)
	if tlsAuthFile != "" {
		tlsConfig.TLSAuth, err = ioutil.ReadFile(tlsAuthFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read TLS auth file: %w", err)
		}
	}

	// Create CA certificate pool
	caCertPool := x509.NewCertPool()
	caCertPool.AddCert(tlsConfig.CACert)

	// Create TLS certificate
	tlsCert := tls.Certificate{
		Certificate: [][]byte{tlsConfig.ServerCert.Raw},
		PrivateKey:  tlsConfig.ServerKey,
	}

	// Configure TLS
	tlsConfig.Config = &tls.Config{
		Certificates: []tls.Certificate{tlsCert},
		ClientAuth:   tls.RequireAndVerifyClientCert,
		ClientCAs:    caCertPool,
		MinVersion:   tls.VersionTLS12,
		CipherSuites: []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
		},
	}

	return tlsConfig, nil
}

// TLSSession represents a TLS session for OpenVPN
type TLSSession struct {
	conn         *tls.Conn
	config       *TLSConfig
	isServer     bool
	state        TLSState
	keyMaterial  []byte
	clientRandom []byte
	serverRandom []byte
	masterSecret []byte
	sessionID    []byte
}

// TLSState represents the state of TLS handshake
type TLSState int

const (
	TLSStateInit TLSState = iota
	TLSStateHandshaking
	TLSStateEstablished
	TLSStateClosed
)

// String returns string representation of TLS state
func (s TLSState) String() string {
	switch s {
	case TLSStateInit:
		return "INIT"
	case TLSStateHandshaking:
		return "HANDSHAKING"
	case TLSStateEstablished:
		return "ESTABLISHED"
	case TLSStateClosed:
		return "CLOSED"
	default:
		return "UNKNOWN"
	}
}

// NewTLSSession creates a new TLS session
func NewTLSSession(config *TLSConfig, isServer bool) *TLSSession {
	return &TLSSession{
		config:   config,
		isServer: isServer,
		state:    TLSStateInit,
	}
}

// ProcessTLSData processes TLS handshake data with proper state management
func (ts *TLSSession) ProcessTLSData(data []byte) ([]byte, error) {
	if ts.state == TLSStateClosed {
		return nil, fmt.Errorf("TLS session is closed")
	}

	if len(data) == 0 {
		return nil, fmt.Errorf("empty TLS data")
	}

	// Parse TLS record header
	if len(data) < 5 {
		return nil, fmt.Errorf("TLS record too short")
	}

	recordType := data[0]
	version := uint16(data[1])<<8 | uint16(data[2])
	length := uint16(data[3])<<8 | uint16(data[4])

	if len(data) < int(5+length) {
		return nil, fmt.Errorf("incomplete TLS record")
	}

	// Validate TLS version
	if version < 0x0301 || version > 0x0304 { // TLS 1.0 to 1.3
		return nil, fmt.Errorf("unsupported TLS version: 0x%04x", version)
	}

	switch ts.state {
	case TLSStateInit:
		return ts.handleClientHello(recordType, data[5:5+length])
	case TLSStateHandshaking:
		return ts.handleHandshakeMessage(recordType, data[5:5+length])
	case TLSStateEstablished:
		return ts.handleApplicationData(recordType, data[5:5+length])
	default:
		return nil, fmt.Errorf("invalid TLS state: %v", ts.state)
	}
}

// handleClientHello processes the initial client hello message
func (ts *TLSSession) handleClientHello(recordType byte, data []byte) ([]byte, error) {
	if recordType != 22 { // Handshake record
		return nil, fmt.Errorf("expected handshake record, got %d", recordType)
	}

	if len(data) < 4 {
		return nil, fmt.Errorf("handshake message too short")
	}

	handshakeType := data[0]
	if handshakeType != 1 { // Client Hello
		return nil, fmt.Errorf("expected client hello, got handshake type %d", handshakeType)
	}

	// Extract client random for key derivation
	if len(data) >= 38 {
		ts.clientRandom = make([]byte, 32)
		copy(ts.clientRandom, data[6:38])
	}

	// Generate server random
	ts.serverRandom = make([]byte, 32)
	if _, err := rand.Read(ts.serverRandom); err != nil {
		return nil, fmt.Errorf("failed to generate server random: %w", err)
	}

	ts.state = TLSStateHandshaking

	// Create server hello response
	return ts.createServerHello()
}

// handleHandshakeMessage processes handshake messages during negotiation
func (ts *TLSSession) handleHandshakeMessage(recordType byte, data []byte) ([]byte, error) {
	if recordType != 22 { // Handshake record
		return nil, fmt.Errorf("expected handshake record during handshake")
	}

	if len(data) < 4 {
		return nil, fmt.Errorf("handshake message too short")
	}

	handshakeType := data[0]

	switch handshakeType {
	case 11: // Certificate
		return ts.handleCertificate(data)
	case 16: // Client Key Exchange
		return ts.handleClientKeyExchange(data)
	case 20: // Finished
		return ts.handleFinished(data)
	default:
		// For other handshake messages, just acknowledge
		return nil, nil
	}
}

// handleApplicationData processes application data after handshake completion
func (ts *TLSSession) handleApplicationData(recordType byte, data []byte) ([]byte, error) {
	if recordType != 23 { // Application Data record
		return nil, fmt.Errorf("expected application data record")
	}

	// In a real implementation, this would decrypt the application data
	// For now, we'll just return the data as-is
	return data, nil
}

// createServerHello creates a server hello message
func (ts *TLSSession) createServerHello() ([]byte, error) {
	// This is a simplified server hello
	// In production, this would be a properly formatted TLS server hello
	serverHello := make([]byte, 0, 100)

	// TLS Record Header
	serverHello = append(serverHello, 22)         // Handshake record
	serverHello = append(serverHello, 0x03, 0x03) // TLS 1.2

	// Handshake message will be added here
	handshakeStart := len(serverHello) + 2 // Skip length field

	// Handshake Header
	serverHello = append(serverHello, 0x00, 0x00)       // Length placeholder
	serverHello = append(serverHello, 2)                // Server Hello
	serverHello = append(serverHello, 0x00, 0x00, 0x00) // Length placeholder

	// Server Version
	serverHello = append(serverHello, 0x03, 0x03) // TLS 1.2

	// Server Random
	serverHello = append(serverHello, ts.serverRandom...)

	// Session ID (empty for now)
	serverHello = append(serverHello, 0x00)

	// Cipher Suite (AES-GCM)
	serverHello = append(serverHello, 0x00, 0x9C)

	// Compression Method (none)
	serverHello = append(serverHello, 0x00)

	// Update lengths
	handshakeLen := len(serverHello) - handshakeStart
	recordLen := len(serverHello) - 5

	// Update record length
	serverHello[3] = byte(recordLen >> 8)
	serverHello[4] = byte(recordLen)

	// Update handshake length
	serverHello[handshakeStart+1] = byte(handshakeLen >> 16)
	serverHello[handshakeStart+2] = byte(handshakeLen >> 8)
	serverHello[handshakeStart+3] = byte(handshakeLen)

	return serverHello, nil
}

// handleCertificate processes client certificate message
func (ts *TLSSession) handleCertificate(data []byte) ([]byte, error) {
	if len(data) < 7 {
		return nil, fmt.Errorf("certificate message too short")
	}

	// Extract certificate chain length
	certChainLen := uint32(data[4])<<16 | uint32(data[5])<<8 | uint32(data[6])

	if len(data) < int(7+certChainLen) {
		return nil, fmt.Errorf("incomplete certificate chain")
	}

	// In a real implementation, we would parse and validate the certificate chain
	// For now, we'll just acknowledge receipt
	return nil, nil
}

// handleClientKeyExchange processes client key exchange message
func (ts *TLSSession) handleClientKeyExchange(data []byte) ([]byte, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("client key exchange message too short")
	}

	// Extract the encrypted pre-master secret
	// In a real implementation, we would decrypt this with our private key
	// and derive the master secret

	// Generate master secret (simplified)
	ts.masterSecret = make([]byte, 48)
	if _, err := rand.Read(ts.masterSecret); err != nil {
		return nil, fmt.Errorf("failed to generate master secret: %w", err)
	}

	// Derive key material from master secret
	ts.keyMaterial = ts.deriveKeyMaterial()

	return nil, nil
}

// handleFinished processes the finished message
func (ts *TLSSession) handleFinished(data []byte) ([]byte, error) {
	if len(data) < 16 {
		return nil, fmt.Errorf("finished message too short")
	}

	// Verify the finished message hash
	// In a real implementation, we would verify the PRF hash

	// Mark handshake as complete
	ts.state = TLSStateEstablished

	// Send our finished message
	return ts.createFinishedMessage()
}

// createFinishedMessage creates a finished message
func (ts *TLSSession) createFinishedMessage() ([]byte, error) {
	// Create a simplified finished message
	finished := make([]byte, 0, 32)

	// TLS Record Header
	finished = append(finished, 22)         // Handshake record
	finished = append(finished, 0x03, 0x03) // TLS 1.2
	finished = append(finished, 0x00, 0x10) // Length: 16 bytes

	// Handshake Header
	finished = append(finished, 20)               // Finished
	finished = append(finished, 0x00, 0x00, 0x0C) // Length: 12 bytes

	// Finished hash (simplified - should be PRF output)
	finishedHash := make([]byte, 12)
	rand.Read(finishedHash)
	finished = append(finished, finishedHash...)

	return finished, nil
}

// deriveKeyMaterial derives encryption keys from master secret
func (ts *TLSSession) deriveKeyMaterial() []byte {
	// This is a simplified key derivation
	// In production, this would use the TLS PRF with proper labels

	keyMaterial := make([]byte, 128) // Enough for all keys

	// Use a simple HMAC-based derivation for now
	h := hmac.New(sha256.New, ts.masterSecret)
	h.Write([]byte("key expansion"))
	h.Write(ts.clientRandom)
	h.Write(ts.serverRandom)

	derived := h.Sum(nil)
	copy(keyMaterial, derived)

	// Generate additional material if needed
	for i := 32; i < len(keyMaterial); i += 32 {
		h.Reset()
		h.Write(derived)
		h.Write([]byte("key expansion"))
		derived = h.Sum(nil)
		copy(keyMaterial[i:], derived)
	}

	return keyMaterial
}

// IsEstablished returns true if TLS handshake is complete
func (ts *TLSSession) IsEstablished() bool {
	return ts.state == TLSStateEstablished
}

// GetKeyMaterial returns the derived key material
func (ts *TLSSession) GetKeyMaterial() []byte {
	return ts.keyMaterial
}

// Close closes the TLS session
func (ts *TLSSession) Close() error {
	ts.state = TLSStateClosed
	if ts.conn != nil {
		return ts.conn.Close()
	}
	return nil
}

// MockTLSConn is a mock TLS connection for testing
type MockTLSConn struct {
	readBuffer  []byte
	writeBuffer []byte
	closed      bool
}

// NewMockTLSConn creates a new mock TLS connection
func NewMockTLSConn() *MockTLSConn {
	return &MockTLSConn{
		readBuffer:  make([]byte, 0),
		writeBuffer: make([]byte, 0),
		closed:      false,
	}
}

// Read implements net.Conn interface
func (m *MockTLSConn) Read(b []byte) (int, error) {
	if m.closed {
		return 0, fmt.Errorf("connection closed")
	}

	if len(m.readBuffer) == 0 {
		return 0, nil
	}

	n := copy(b, m.readBuffer)
	m.readBuffer = m.readBuffer[n:]
	return n, nil
}

// Write implements net.Conn interface
func (m *MockTLSConn) Write(b []byte) (int, error) {
	if m.closed {
		return 0, fmt.Errorf("connection closed")
	}

	m.writeBuffer = append(m.writeBuffer, b...)
	return len(b), nil
}

// Close implements net.Conn interface
func (m *MockTLSConn) Close() error {
	m.closed = true
	return nil
}

// LocalAddr implements net.Conn interface
func (m *MockTLSConn) LocalAddr() net.Addr {
	return &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 1194}
}

// RemoteAddr implements net.Conn interface
func (m *MockTLSConn) RemoteAddr() net.Addr {
	return &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 12345}
}

// SetDeadline implements net.Conn interface
func (m *MockTLSConn) SetDeadline(t time.Time) error {
	return nil
}

// SetReadDeadline implements net.Conn interface
func (m *MockTLSConn) SetReadDeadline(t time.Time) error {
	return nil
}

// SetWriteDeadline implements net.Conn interface
func (m *MockTLSConn) SetWriteDeadline(t time.Time) error {
	return nil
}

// AddReadData adds data to the read buffer (for testing)
func (m *MockTLSConn) AddReadData(data []byte) {
	m.readBuffer = append(m.readBuffer, data...)
}

// GetWrittenData returns data written to the connection (for testing)
func (m *MockTLSConn) GetWrittenData() []byte {
	data := make([]byte, len(m.writeBuffer))
	copy(data, m.writeBuffer)
	m.writeBuffer = m.writeBuffer[:0] // Clear buffer
	return data
}
