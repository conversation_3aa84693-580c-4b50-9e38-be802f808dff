package crypto

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"net"
	"time"
)

// TLSConfig holds TLS configuration for OpenVPN
type TLSConfig struct {
	CACert     *x509.Certificate
	ServerCert *x509.Certificate
	ServerKey  *rsa.PrivateKey
	TLSAuth    []byte
	Config     *tls.Config
}

// LoadTLSConfig loads TLS configuration from files
func LoadTLSConfig(caCertFile, serverCertFile, serverKeyFile, tlsAuthFile string) (*TLSConfig, error) {
	tlsConfig := &TLSConfig{}
	
	// Load CA certificate
	caCertPEM, err := ioutil.ReadFile(caCertFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read CA certificate: %w", err)
	}
	
	caCertBlock, _ := pem.Decode(caCertPEM)
	if caCertBlock == nil {
		return nil, fmt.Errorf("failed to decode CA certificate PEM")
	}
	
	tlsConfig.CACert, err = x509.ParseCertificate(caCertBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CA certificate: %w", err)
	}
	
	// Load server certificate
	serverCertPEM, err := ioutil.ReadFile(serverCertFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read server certificate: %w", err)
	}
	
	serverCertBlock, _ := pem.Decode(serverCertPEM)
	if serverCertBlock == nil {
		return nil, fmt.Errorf("failed to decode server certificate PEM")
	}
	
	tlsConfig.ServerCert, err = x509.ParseCertificate(serverCertBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse server certificate: %w", err)
	}
	
	// Load server private key
	serverKeyPEM, err := ioutil.ReadFile(serverKeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read server key: %w", err)
	}
	
	serverKeyBlock, _ := pem.Decode(serverKeyPEM)
	if serverKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode server key PEM")
	}
	
	serverKey, err := x509.ParsePKCS1PrivateKey(serverKeyBlock.Bytes)
	if err != nil {
		// Try PKCS8 format
		keyInterface, err := x509.ParsePKCS8PrivateKey(serverKeyBlock.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse server key: %w", err)
		}
		var ok bool
		serverKey, ok = keyInterface.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("server key is not RSA private key")
		}
	}
	tlsConfig.ServerKey = serverKey
	
	// Load TLS auth key (optional)
	if tlsAuthFile != "" {
		tlsConfig.TLSAuth, err = ioutil.ReadFile(tlsAuthFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read TLS auth file: %w", err)
		}
	}
	
	// Create CA certificate pool
	caCertPool := x509.NewCertPool()
	caCertPool.AddCert(tlsConfig.CACert)
	
	// Create TLS certificate
	tlsCert := tls.Certificate{
		Certificate: [][]byte{tlsConfig.ServerCert.Raw},
		PrivateKey:  tlsConfig.ServerKey,
	}
	
	// Configure TLS
	tlsConfig.Config = &tls.Config{
		Certificates: []tls.Certificate{tlsCert},
		ClientAuth:   tls.RequireAndVerifyClientCert,
		ClientCAs:    caCertPool,
		MinVersion:   tls.VersionTLS12,
		CipherSuites: []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
		},
	}
	
	return tlsConfig, nil
}

// TLSSession represents a TLS session for OpenVPN
type TLSSession struct {
	conn      *tls.Conn
	config    *TLSConfig
	isServer  bool
	state     TLSState
	keyMaterial []byte
}

// TLSState represents the state of TLS handshake
type TLSState int

const (
	TLSStateInit TLSState = iota
	TLSStateHandshaking
	TLSStateEstablished
	TLSStateClosed
)

// String returns string representation of TLS state
func (s TLSState) String() string {
	switch s {
	case TLSStateInit:
		return "INIT"
	case TLSStateHandshaking:
		return "HANDSHAKING"
	case TLSStateEstablished:
		return "ESTABLISHED"
	case TLSStateClosed:
		return "CLOSED"
	default:
		return "UNKNOWN"
	}
}

// NewTLSSession creates a new TLS session
func NewTLSSession(config *TLSConfig, isServer bool) *TLSSession {
	return &TLSSession{
		config:   config,
		isServer: isServer,
		state:    TLSStateInit,
	}
}

// ProcessTLSData processes TLS handshake data
func (ts *TLSSession) ProcessTLSData(data []byte) ([]byte, error) {
	if ts.state == TLSStateClosed {
		return nil, fmt.Errorf("TLS session is closed")
	}
	
	// In a real implementation, this would process TLS handshake messages
	// For now, we'll simulate the handshake process
	
	if ts.state == TLSStateInit {
		ts.state = TLSStateHandshaking
		// Return server hello or similar
		return []byte("TLS_SERVER_HELLO"), nil
	}
	
	if ts.state == TLSStateHandshaking {
		ts.state = TLSStateEstablished
		// Generate key material
		ts.keyMaterial = make([]byte, 64) // 64 bytes for key material
		rand.Read(ts.keyMaterial)
		return []byte("TLS_FINISHED"), nil
	}
	
	return nil, nil
}

// IsEstablished returns true if TLS handshake is complete
func (ts *TLSSession) IsEstablished() bool {
	return ts.state == TLSStateEstablished
}

// GetKeyMaterial returns the derived key material
func (ts *TLSSession) GetKeyMaterial() []byte {
	return ts.keyMaterial
}

// Close closes the TLS session
func (ts *TLSSession) Close() error {
	ts.state = TLSStateClosed
	if ts.conn != nil {
		return ts.conn.Close()
	}
	return nil
}

// MockTLSConn is a mock TLS connection for testing
type MockTLSConn struct {
	readBuffer  []byte
	writeBuffer []byte
	closed      bool
}

// NewMockTLSConn creates a new mock TLS connection
func NewMockTLSConn() *MockTLSConn {
	return &MockTLSConn{
		readBuffer:  make([]byte, 0),
		writeBuffer: make([]byte, 0),
		closed:      false,
	}
}

// Read implements net.Conn interface
func (m *MockTLSConn) Read(b []byte) (n int, error) {
	if m.closed {
		return 0, fmt.Errorf("connection closed")
	}
	
	if len(m.readBuffer) == 0 {
		return 0, nil
	}
	
	n = copy(b, m.readBuffer)
	m.readBuffer = m.readBuffer[n:]
	return n, nil
}

// Write implements net.Conn interface
func (m *MockTLSConn) Write(b []byte) (n int, error) {
	if m.closed {
		return 0, fmt.Errorf("connection closed")
	}
	
	m.writeBuffer = append(m.writeBuffer, b...)
	return len(b), nil
}

// Close implements net.Conn interface
func (m *MockTLSConn) Close() error {
	m.closed = true
	return nil
}

// LocalAddr implements net.Conn interface
func (m *MockTLSConn) LocalAddr() net.Addr {
	return &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 1194}
}

// RemoteAddr implements net.Conn interface
func (m *MockTLSConn) RemoteAddr() net.Addr {
	return &net.TCPAddr{IP: net.ParseIP("127.0.0.1"), Port: 12345}
}

// SetDeadline implements net.Conn interface
func (m *MockTLSConn) SetDeadline(t time.Time) error {
	return nil
}

// SetReadDeadline implements net.Conn interface
func (m *MockTLSConn) SetReadDeadline(t time.Time) error {
	return nil
}

// SetWriteDeadline implements net.Conn interface
func (m *MockTLSConn) SetWriteDeadline(t time.Time) error {
	return nil
}

// AddReadData adds data to the read buffer (for testing)
func (m *MockTLSConn) AddReadData(data []byte) {
	m.readBuffer = append(m.readBuffer, data...)
}

// GetWrittenData returns data written to the connection (for testing)
func (m *MockTLSConn) GetWrittenData() []byte {
	data := make([]byte, len(m.writeBuffer))
	copy(data, m.writeBuffer)
	m.writeBuffer = m.writeBuffer[:0] // Clear buffer
	return data
}
