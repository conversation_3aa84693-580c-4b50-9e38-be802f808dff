# OpenVPN Server Configuration
# Sample configuration for OpenVPN server

# Network settings
port 1194
proto udp
local 0.0.0.0

# Device settings
dev tun

# Certificate and key files
ca certs/ca.crt
cert certs/server.crt
key certs/server.key
dh certs/dh2048.pem
tls-auth certs/ta.key 0

# VPN network settings
server 10.8.0.0 *************

# Client settings
max-clients 100
keepalive 10 120

# Push routes to clients
push "route *********** *************"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Uncomment to redirect all client traffic through VPN
# push "redirect-gateway def1"

# Allow client-to-client communication
# client-to-client

# Enable compression (optional)
# comp-lzo

# Logging
log logs/openvpn.log
verb 3

# Management interface (optional)
# management 127.0.0.1 7505

# Security settings
cipher AES-256-GCM
auth SHA256

# Allow multiple clients with same certificate (not recommended for production)
# duplicate-cn
