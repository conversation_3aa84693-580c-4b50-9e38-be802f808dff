package protocol

import (
	"bytes"
	"testing"
)

func TestParsePacket(t *testing.T) {
	// Test data for a control packet
	sessionID := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}
	payload := []byte("test payload")
	
	// Create test packet data
	data := make([]byte, 0)
	data = append(data, (P_CONTROL_V1<<3)|0x01) // opcode + key_id
	data = append(data, sessionID...)            // session_id
	data = append(data, 0x00, 0x00, 0x00, 0x01) // packet_id (big endian)
	data = append(data, payload...)              // payload
	
	// Parse the packet
	packet, err := ParsePacket(data)
	if err != nil {
		t.Fatalf("Failed to parse packet: %v", err)
	}
	
	// Verify parsed data
	if packet.Header.Opcode != P_CONTROL_V1 {
		t.Errorf("Expected opcode %d, got %d", P_CONTROL_V1, packet.Header.Opcode)
	}
	
	if packet.Header.KeyID != 0x01 {
		t.<PERSON>rrorf("Expected key ID %d, got %d", 0x01, packet.Header.KeyID)
	}
	
	if !bytes.Equal(packet.Header.SessionID, sessionID) {
		t.Errorf("Expected session ID %v, got %v", sessionID, packet.Header.SessionID)
	}
	
	if packet.Header.PacketID != 1 {
		t.Errorf("Expected packet ID %d, got %d", 1, packet.Header.PacketID)
	}
	
	if !bytes.Equal(packet.Payload, payload) {
		t.Errorf("Expected payload %v, got %v", payload, packet.Payload)
	}
}

func TestSerializePacket(t *testing.T) {
	sessionID := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}
	payload := []byte("test payload")
	
	packet := &Packet{
		Header: PacketHeader{
			Opcode:    P_CONTROL_V1,
			KeyID:     0x01,
			SessionID: sessionID,
			PacketID:  1,
		},
		Payload: payload,
	}
	
	data, err := packet.SerializePacket()
	if err != nil {
		t.Fatalf("Failed to serialize packet: %v", err)
	}
	
	// Parse it back
	parsedPacket, err := ParsePacket(data)
	if err != nil {
		t.Fatalf("Failed to parse serialized packet: %v", err)
	}
	
	// Verify round-trip
	if parsedPacket.Header.Opcode != packet.Header.Opcode {
		t.Errorf("Opcode mismatch after round-trip")
	}
	
	if parsedPacket.Header.KeyID != packet.Header.KeyID {
		t.Errorf("Key ID mismatch after round-trip")
	}
	
	if !bytes.Equal(parsedPacket.Header.SessionID, packet.Header.SessionID) {
		t.Errorf("Session ID mismatch after round-trip")
	}
	
	if parsedPacket.Header.PacketID != packet.Header.PacketID {
		t.Errorf("Packet ID mismatch after round-trip")
	}
	
	if !bytes.Equal(parsedPacket.Payload, packet.Payload) {
		t.Errorf("Payload mismatch after round-trip")
	}
}

func TestIsControlPacket(t *testing.T) {
	controlOpcodes := []uint8{
		P_CONTROL_HARD_RESET_CLIENT_V1,
		P_CONTROL_HARD_RESET_SERVER_V1,
		P_CONTROL_SOFT_RESET_V1,
		P_CONTROL_V1,
		P_ACK_V1,
		P_CONTROL_HARD_RESET_CLIENT_V2,
		P_CONTROL_HARD_RESET_SERVER_V2,
	}
	
	for _, opcode := range controlOpcodes {
		if !IsControlPacket(opcode) {
			t.Errorf("Opcode %d should be identified as control packet", opcode)
		}
	}
	
	dataOpcodes := []uint8{P_DATA_V1, P_DATA_V2}
	for _, opcode := range dataOpcodes {
		if IsControlPacket(opcode) {
			t.Errorf("Opcode %d should not be identified as control packet", opcode)
		}
	}
}

func TestIsDataPacket(t *testing.T) {
	dataOpcodes := []uint8{P_DATA_V1, P_DATA_V2}
	for _, opcode := range dataOpcodes {
		if !IsDataPacket(opcode) {
			t.Errorf("Opcode %d should be identified as data packet", opcode)
		}
	}
	
	controlOpcodes := []uint8{
		P_CONTROL_HARD_RESET_CLIENT_V1,
		P_CONTROL_V1,
		P_ACK_V1,
	}
	for _, opcode := range controlOpcodes {
		if IsDataPacket(opcode) {
			t.Errorf("Opcode %d should not be identified as data packet", opcode)
		}
	}
}

func TestNewControlPacket(t *testing.T) {
	sessionID := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}
	payload := []byte("test")
	
	packet := NewControlPacket(P_CONTROL_V1, 0x02, sessionID, 123, payload)
	
	if packet.Header.Opcode != P_CONTROL_V1 {
		t.Errorf("Expected opcode %d, got %d", P_CONTROL_V1, packet.Header.Opcode)
	}
	
	if packet.Header.KeyID != 0x02 {
		t.Errorf("Expected key ID %d, got %d", 0x02, packet.Header.KeyID)
	}
	
	if !bytes.Equal(packet.Header.SessionID, sessionID) {
		t.Errorf("Expected session ID %v, got %v", sessionID, packet.Header.SessionID)
	}
	
	if packet.Header.PacketID != 123 {
		t.Errorf("Expected packet ID %d, got %d", 123, packet.Header.PacketID)
	}
	
	if !bytes.Equal(packet.Payload, payload) {
		t.Errorf("Expected payload %v, got %v", payload, packet.Payload)
	}
}

func TestNewDataPacket(t *testing.T) {
	sessionID := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}
	data := []byte("data payload")
	
	packet := NewDataPacket(0x03, sessionID, data)
	
	if packet.Header.Opcode != P_DATA_V1 {
		t.Errorf("Expected opcode %d, got %d", P_DATA_V1, packet.Header.Opcode)
	}
	
	if packet.Header.KeyID != 0x03 {
		t.Errorf("Expected key ID %d, got %d", 0x03, packet.Header.KeyID)
	}
	
	if !bytes.Equal(packet.Header.SessionID, sessionID) {
		t.Errorf("Expected session ID %v, got %v", sessionID, packet.Header.SessionID)
	}
	
	if !bytes.Equal(packet.Payload, data) {
		t.Errorf("Expected payload %v, got %v", data, packet.Payload)
	}
}

func TestPacketTooShort(t *testing.T) {
	// Test with packet too short for header
	shortData := []byte{0x01, 0x02}
	
	_, err := ParsePacket(shortData)
	if err == nil {
		t.Error("Expected error for packet too short")
	}
}

func TestGetOpcodeString(t *testing.T) {
	testCases := map[uint8]string{
		P_CONTROL_HARD_RESET_CLIENT_V1: "P_CONTROL_HARD_RESET_CLIENT_V1",
		P_CONTROL_HARD_RESET_SERVER_V1: "P_CONTROL_HARD_RESET_SERVER_V1",
		P_CONTROL_V1:                   "P_CONTROL_V1",
		P_DATA_V1:                      "P_DATA_V1",
		255:                            "UNKNOWN_OPCODE_255",
	}
	
	for opcode, expected := range testCases {
		result := GetOpcodeString(opcode)
		if result != expected {
			t.Errorf("Expected %s for opcode %d, got %s", expected, opcode, result)
		}
	}
}
