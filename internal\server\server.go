package server

import (
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"openvpn-server/internal/config"
	"openvpn-server/internal/crypto"
	"openvpn-server/internal/network"
	"openvpn-server/internal/protocol"
)

// Server represents the OpenVPN server
type Server struct {
	config       *config.Config
	tlsConfig    *crypto.TLSConfig
	listener     net.PacketConn
	tunInterface *network.TunTapInterface
	ipPool       *network.IPPool
	routeManager *network.RouteManager
	natManager   *network.NATManager
	clients      map[string]*ClientSession
	clientsMutex sync.RWMutex
	running      bool
	stopChan     chan struct{}
}

// New creates a new OpenVPN server
func New(cfg *config.Config) (*Server, error) {
	// Load TLS configuration
	tlsConfig, err := crypto.LoadTLSConfig(
		cfg.CACertFile,
		cfg.ServerCertFile,
		cfg.ServerKeyFile,
		cfg.TLSAuthFile,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load TLS config: %w", err)
	}

	// Parse server network
	serverIP := net.ParseIP(cfg.ServerIP)
	if serverIP == nil {
		return nil, fmt.Errorf("invalid server IP: %s", cfg.ServerIP)
	}

	// Create IP pool
	ipPool, err := network.NewIPPool(cfg.ServerNetwork, serverIP)
	if err != nil {
		return nil, fmt.Errorf("failed to create IP pool: %w", err)
	}

	server := &Server{
		config:       cfg,
		tlsConfig:    tlsConfig,
		ipPool:       ipPool,
		routeManager: network.NewRouteManager(),
		natManager:   network.NewNATManager(),
		clients:      make(map[string]*ClientSession),
		stopChan:     make(chan struct{}),
	}

	return server, nil
}

// Start starts the OpenVPN server
func (s *Server) Start() error {
	log.Printf("Starting OpenVPN server on %s:%d (%s)",
		s.config.ListenAddress, s.config.Port, s.config.Protocol)

	// Create TUN interface
	if err := s.createTunInterface(); err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}
	defer s.tunInterface.Close()

	// Start listening for client connections
	if err := s.startListener(); err != nil {
		return fmt.Errorf("failed to start listener: %w", err)
	}
	defer s.listener.Close()

	s.running = true

	// Start packet processing goroutines
	go s.handleTunPackets()
	go s.handleClientPackets()

	log.Println("OpenVPN server started successfully")

	// Wait for stop signal
	<-s.stopChan

	log.Println("OpenVPN server stopping...")
	s.running = false

	// Clean up clients
	s.clientsMutex.Lock()
	for _, client := range s.clients {
		client.Close()
	}
	s.clientsMutex.Unlock()

	return nil
}

// Stop stops the OpenVPN server
func (s *Server) Stop() {
	if s.running {
		close(s.stopChan)
	}
}

// createTunInterface creates the TUN interface
func (s *Server) createTunInterface() error {
	serverIP := net.ParseIP(s.config.ServerIP)
	_, ipNet, err := net.ParseCIDR(s.config.ServerNetwork)
	if err != nil {
		return fmt.Errorf("invalid server network: %w", err)
	}

	// For testing purposes, we'll create a placeholder interface
	// In production, this would create a real TUN interface
	s.tunInterface, err = network.NewTunInterface(
		s.config.DeviceName,
		serverIP,
		ipNet.Mask,
		1500, // MTU
	)
	if err != nil {
		// Fall back to mock for development/testing
		log.Printf("Failed to create real TUN interface, using mock: %v", err)
		return nil // For now, we'll skip TUN interface creation
	}

	log.Printf("Created TUN interface: %s (%s)",
		s.config.DeviceName, s.config.ServerIP)

	return nil
}

// startListener starts the UDP/TCP listener
func (s *Server) startListener() error {
	address := fmt.Sprintf("%s:%d", s.config.ListenAddress, s.config.Port)

	var err error
	if s.config.Protocol == "udp" {
		s.listener, err = net.ListenPacket("udp", address)
	} else {
		return fmt.Errorf("TCP protocol not yet implemented")
	}

	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", address, err)
	}

	log.Printf("Listening on %s (%s)", address, s.config.Protocol)
	return nil
}

// handleClientPackets handles incoming packets from clients
func (s *Server) handleClientPackets() {
	buffer := make([]byte, 2048)

	for s.running {
		n, clientAddr, err := s.listener.ReadFrom(buffer)
		if err != nil {
			if s.running {
				log.Printf("Error reading from listener: %v", err)
			}
			continue
		}

		// Process the packet
		go s.processClientPacket(buffer[:n], clientAddr)
	}
}

// processClientPacket processes a packet from a client
func (s *Server) processClientPacket(data []byte, clientAddr net.Addr) {
	// Parse the OpenVPN packet
	packet, err := protocol.ParsePacket(data)
	if err != nil {
		log.Printf("Failed to parse packet from %s: %v", clientAddr, err)
		return
	}

	// Get or create client session
	clientID := clientAddr.String()
	client := s.getOrCreateClient(clientID, clientAddr)

	// Process the packet
	response, err := client.ProcessPacket(packet)
	if err != nil {
		log.Printf("Error processing packet from %s: %v", clientAddr, err)
		return
	}

	// Send response if any
	if response != nil {
		responseData, err := response.SerializePacket()
		if err != nil {
			log.Printf("Failed to serialize response: %v", err)
			return
		}

		_, err = s.listener.WriteTo(responseData, clientAddr)
		if err != nil {
			log.Printf("Failed to send response to %s: %v", clientAddr, err)
		}
	}
}

// getOrCreateClient gets an existing client or creates a new one
func (s *Server) getOrCreateClient(clientID string, addr net.Addr) *ClientSession {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	client, exists := s.clients[clientID]
	if !exists {
		client = NewClientSession(clientID, addr, s.config, s.tlsConfig, s.ipPool)
		s.clients[clientID] = client
		log.Printf("New client session: %s", clientID)
	}

	return client
}

// handleTunPackets handles packets from the TUN interface
func (s *Server) handleTunPackets() {
	buffer := make([]byte, 2048)

	for s.running {
		if s.tunInterface == nil || !s.tunInterface.IsOpen() {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		n, err := s.tunInterface.Read(buffer)
		if err != nil {
			if s.running {
				log.Printf("Error reading from TUN interface: %v", err)
			}
			continue
		}

		// Process TUN packet
		s.processTunPacket(buffer[:n])
	}
}

// processTunPacket processes a packet from the TUN interface
func (s *Server) processTunPacket(data []byte) {
	// Parse IP packet to determine destination
	if len(data) < 20 {
		return // Too short for IP header
	}

	// Extract destination IP (bytes 16-19 for IPv4)
	destIP := net.IP(data[16:20])

	// Find client with this IP
	s.clientsMutex.RLock()
	var targetClient *ClientSession
	for _, client := range s.clients {
		if client.GetAllocatedIP().Equal(destIP) {
			targetClient = client
			break
		}
	}
	s.clientsMutex.RUnlock()

	if targetClient != nil {
		// Send packet to client
		targetClient.SendDataPacket(data)
	}
}

// GetClientCount returns the number of connected clients
func (s *Server) GetClientCount() int {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()
	return len(s.clients)
}

// GetServerStats returns server statistics
func (s *Server) GetServerStats() ServerStats {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	stats := ServerStats{
		ClientCount:   len(s.clients),
		AvailableIPs:  s.ipPool.GetAvailableCount(),
		AllocatedIPs:  s.ipPool.GetAllocatedCount(),
		ServerIP:      s.config.ServerIP,
		ServerNetwork: s.config.ServerNetwork,
		ListenAddress: s.config.ListenAddress,
		Port:          s.config.Port,
		Protocol:      s.config.Protocol,
	}

	return stats
}

// ServerStats represents server statistics
type ServerStats struct {
	ClientCount   int    `json:"client_count"`
	AvailableIPs  int    `json:"available_ips"`
	AllocatedIPs  int    `json:"allocated_ips"`
	ServerIP      string `json:"server_ip"`
	ServerNetwork string `json:"server_network"`
	ListenAddress string `json:"listen_address"`
	Port          int    `json:"port"`
	Protocol      string `json:"protocol"`
}
