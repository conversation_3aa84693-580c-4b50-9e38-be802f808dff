package server

import (
	"fmt"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"openvpn-server/internal/config"
	"openvpn-server/internal/crypto"
	"openvpn-server/internal/network"
	"openvpn-server/internal/protocol"
)

// ServerStatistics tracks server performance metrics
type ServerStatistics struct {
	StartTime         time.Time
	PacketsReceived   uint64
	PacketsSent       uint64
	BytesReceived     uint64
	BytesSent         uint64
	ClientConnections uint64
	ActiveClients     uint64
	ErrorCount        uint64
	mutex             sync.RWMutex
}

// RateLimiter implements token bucket rate limiting
type RateLimiter struct {
	tokens     int64
	maxTokens  int64
	refillRate int64
	lastRefill time.Time
	mutex      sync.Mutex
}

// ConnectionPool manages connection resources
type ConnectionPool struct {
	connections chan net.Conn
	maxSize     int
	created     int
	mutex       sync.Mutex
}

// WorkerPool manages worker goroutines for packet processing
type WorkerPool struct {
	workers   int
	taskQueue chan func()
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// Server represents the OpenVPN server
type Server struct {
	config       *config.Config
	tlsConfig    *crypto.TLSConfig
	listener     net.PacketConn
	tunInterface *network.TunTapInterface
	ipPool       *network.IPPool
	routeManager *network.RouteManager
	natManager   *network.NATManager
	clients      map[string]*ClientSession
	clientsMutex sync.RWMutex
	running      bool
	stopChan     chan struct{}

	// Performance and monitoring
	packetPool  sync.Pool
	stats       *ServerStatistics
	rateLimiter *RateLimiter

	// Connection management
	connectionPool *ConnectionPool
	workerPool     *WorkerPool
}

// New creates a new OpenVPN server
func New(cfg *config.Config) (*Server, error) {
	// Load TLS configuration
	tlsConfig, err := crypto.LoadTLSConfig(
		cfg.CACertFile,
		cfg.ServerCertFile,
		cfg.ServerKeyFile,
		cfg.TLSAuthFile,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load TLS config: %w", err)
	}

	// Parse server network
	serverIP := net.ParseIP(cfg.ServerIP)
	if serverIP == nil {
		return nil, fmt.Errorf("invalid server IP: %s", cfg.ServerIP)
	}

	// Create IP pool
	ipPool, err := network.NewIPPool(cfg.ServerNetwork, serverIP)
	if err != nil {
		return nil, fmt.Errorf("failed to create IP pool: %w", err)
	}

	// Initialize performance components
	stats := &ServerStatistics{
		StartTime: time.Now(),
	}

	rateLimiter := &RateLimiter{
		maxTokens:  1000, // Max 1000 packets per second
		refillRate: 100,  // Refill 100 tokens per second
		tokens:     1000,
		lastRefill: time.Now(),
	}

	connectionPool := &ConnectionPool{
		connections: make(chan net.Conn, 100),
		maxSize:     100,
	}

	workerPool := &WorkerPool{
		workers:   10, // 10 worker goroutines
		taskQueue: make(chan func(), 1000),
		stopChan:  make(chan struct{}),
	}

	server := &Server{
		config:         cfg,
		tlsConfig:      tlsConfig,
		ipPool:         ipPool,
		routeManager:   network.NewRouteManager(),
		natManager:     network.NewNATManager(),
		clients:        make(map[string]*ClientSession),
		stopChan:       make(chan struct{}),
		stats:          stats,
		rateLimiter:    rateLimiter,
		connectionPool: connectionPool,
		workerPool:     workerPool,
		packetPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 2048) // Reusable packet buffers
			},
		},
	}

	return server, nil
}

// Start starts the OpenVPN server
func (s *Server) Start() error {
	log.Printf("Starting OpenVPN server on %s:%d (%s)",
		s.config.ListenAddress, s.config.Port, s.config.Protocol)

	// Create TUN interface
	if err := s.createTunInterface(); err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}
	defer s.tunInterface.Close()

	// Start listening for client connections
	if err := s.startListener(); err != nil {
		return fmt.Errorf("failed to start listener: %w", err)
	}
	defer s.listener.Close()

	s.running = true

	// Start worker pool
	s.workerPool.Start()

	// Start packet processing goroutines
	go s.handleTunPackets()
	go s.handleClientPackets()

	log.Println("OpenVPN server started successfully")
	log.Printf("Worker pool started with %d workers", s.workerPool.workers)
	log.Printf("Rate limiter: %d tokens/sec max", s.rateLimiter.maxTokens)

	// Wait for stop signal
	<-s.stopChan

	log.Println("OpenVPN server stopping...")
	s.running = false

	// Stop worker pool
	s.workerPool.Stop()
	log.Println("Worker pool stopped")

	// Clean up clients
	s.clientsMutex.Lock()
	clientCount := len(s.clients)
	for _, client := range s.clients {
		client.Close()
	}
	s.clientsMutex.Unlock()
	log.Printf("Closed %d client connections", clientCount)

	// Close connection pool
	close(s.connectionPool.connections)
	for conn := range s.connectionPool.connections {
		conn.Close()
	}

	// Log final statistics
	s.logFinalStatistics()

	return nil
}

// logFinalStatistics logs final server statistics
func (s *Server) logFinalStatistics() {
	uptime := time.Since(s.stats.StartTime)
	log.Printf("Server Statistics:")
	log.Printf("  Uptime: %v", uptime)
	log.Printf("  Packets Received: %d", atomic.LoadUint64(&s.stats.PacketsReceived))
	log.Printf("  Packets Sent: %d", atomic.LoadUint64(&s.stats.PacketsSent))
	log.Printf("  Bytes Received: %d", atomic.LoadUint64(&s.stats.BytesReceived))
	log.Printf("  Bytes Sent: %d", atomic.LoadUint64(&s.stats.BytesSent))
	log.Printf("  Client Connections: %d", atomic.LoadUint64(&s.stats.ClientConnections))
	log.Printf("  Errors: %d", atomic.LoadUint64(&s.stats.ErrorCount))
}

// Stop stops the OpenVPN server
func (s *Server) Stop() {
	if s.running {
		close(s.stopChan)
	}
}

// createTunInterface creates the TUN interface
func (s *Server) createTunInterface() error {
	serverIP := net.ParseIP(s.config.ServerIP)
	_, ipNet, err := net.ParseCIDR(s.config.ServerNetwork)
	if err != nil {
		return fmt.Errorf("invalid server network: %w", err)
	}

	// For testing purposes, we'll create a placeholder interface
	// In production, this would create a real TUN interface
	s.tunInterface, err = network.NewTunInterface(
		s.config.DeviceName,
		serverIP,
		ipNet.Mask,
		1500, // MTU
	)
	if err != nil {
		// Fall back to mock for development/testing
		log.Printf("Failed to create real TUN interface, using mock: %v", err)
		return nil // For now, we'll skip TUN interface creation
	}

	log.Printf("Created TUN interface: %s (%s)",
		s.config.DeviceName, s.config.ServerIP)

	return nil
}

// startListener starts the UDP/TCP listener
func (s *Server) startListener() error {
	address := fmt.Sprintf("%s:%d", s.config.ListenAddress, s.config.Port)

	var err error
	if s.config.Protocol == "udp" {
		s.listener, err = net.ListenPacket("udp", address)
	} else {
		return fmt.Errorf("TCP protocol not yet implemented")
	}

	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", address, err)
	}

	log.Printf("Listening on %s (%s)", address, s.config.Protocol)
	return nil
}

// handleClientPackets handles incoming packets from clients with rate limiting
func (s *Server) handleClientPackets() {
	for s.running {
		// Get buffer from pool
		buffer := s.packetPool.Get().([]byte)

		n, clientAddr, err := s.listener.ReadFrom(buffer)
		if err != nil {
			s.packetPool.Put(buffer)
			if s.running {
				log.Printf("Error reading from listener: %v", err)
				atomic.AddUint64(&s.stats.ErrorCount, 1)
			}
			continue
		}

		// Rate limiting check
		if !s.rateLimiter.Allow() {
			s.packetPool.Put(buffer)
			log.Printf("Rate limit exceeded, dropping packet from %s", clientAddr)
			continue
		}

		// Update statistics
		atomic.AddUint64(&s.stats.PacketsReceived, 1)
		atomic.AddUint64(&s.stats.BytesReceived, uint64(n))

		// Submit to worker pool for processing
		packetData := make([]byte, n)
		copy(packetData, buffer[:n])
		s.packetPool.Put(buffer)

		s.workerPool.Submit(func() {
			s.processClientPacket(packetData, clientAddr)
		})
	}
}

// processClientPacket processes a packet from a client
func (s *Server) processClientPacket(data []byte, clientAddr net.Addr) {
	// Parse the OpenVPN packet
	packet, err := protocol.ParsePacket(data)
	if err != nil {
		log.Printf("Failed to parse packet from %s: %v", clientAddr, err)
		return
	}

	// Get or create client session
	clientID := clientAddr.String()
	client := s.getOrCreateClient(clientID, clientAddr)

	// Process the packet
	response, err := client.ProcessPacket(packet)
	if err != nil {
		log.Printf("Error processing packet from %s: %v", clientAddr, err)
		return
	}

	// Send response if any
	if response != nil {
		responseData, err := response.SerializePacket()
		if err != nil {
			log.Printf("Failed to serialize response: %v", err)
			return
		}

		_, err = s.listener.WriteTo(responseData, clientAddr)
		if err != nil {
			log.Printf("Failed to send response to %s: %v", clientAddr, err)
		}
	}
}

// getOrCreateClient gets an existing client or creates a new one
func (s *Server) getOrCreateClient(clientID string, addr net.Addr) *ClientSession {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	client, exists := s.clients[clientID]
	if !exists {
		client = NewClientSession(clientID, addr, s.config, s.tlsConfig, s.ipPool)
		s.clients[clientID] = client
		log.Printf("New client session: %s", clientID)
	}

	return client
}

// handleTunPackets handles packets from the TUN interface with proper forwarding
func (s *Server) handleTunPackets() {
	for s.running {
		if s.tunInterface == nil || !s.tunInterface.IsOpen() {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		// Get buffer from pool
		buffer := s.packetPool.Get().([]byte)

		n, err := s.tunInterface.Read(buffer)
		if err != nil {
			s.packetPool.Put(buffer)
			if s.running {
				log.Printf("Error reading from TUN interface: %v", err)
				atomic.AddUint64(&s.stats.ErrorCount, 1)
			}
			continue
		}

		// Update statistics
		atomic.AddUint64(&s.stats.PacketsReceived, 1)
		atomic.AddUint64(&s.stats.BytesReceived, uint64(n))

		// Copy packet data and return buffer to pool
		packetData := make([]byte, n)
		copy(packetData, buffer[:n])
		s.packetPool.Put(buffer)

		// Submit to worker pool for processing
		s.workerPool.Submit(func() {
			s.processTunPacket(packetData)
		})
	}
}

// processTunPacket processes a packet from the TUN interface with enhanced routing
func (s *Server) processTunPacket(data []byte) {
	// Validate IP packet
	if len(data) < 20 {
		log.Printf("TUN packet too short: %d bytes", len(data))
		return
	}

	// Parse IP header
	version := (data[0] >> 4) & 0xF
	if version != 4 {
		log.Printf("Unsupported IP version: %d", version)
		return
	}

	headerLen := int(data[0]&0xF) * 4
	if len(data) < headerLen {
		log.Printf("Invalid IP header length: %d", headerLen)
		return
	}

	// Extract source and destination IPs
	srcIP := net.IP(data[12:16])
	destIP := net.IP(data[16:20])

	log.Printf("TUN packet: %s -> %s (%d bytes)", srcIP, destIP, len(data))

	// Check if destination is within our VPN network
	_, vpnNet, err := net.ParseCIDR(s.config.ServerNetwork)
	if err != nil {
		log.Printf("Error parsing VPN network: %v", err)
		return
	}

	if !vpnNet.Contains(destIP) {
		// Packet destined outside VPN network - handle NAT/forwarding
		s.handleExternalPacket(data, srcIP, destIP)
		return
	}

	// Find client with this destination IP
	s.clientsMutex.RLock()
	var targetClient *ClientSession
	for _, client := range s.clients {
		if client.IsConnected() && client.GetAllocatedIP().Equal(destIP) {
			targetClient = client
			break
		}
	}
	s.clientsMutex.RUnlock()

	if targetClient != nil {
		// Send packet to target client
		if err := targetClient.SendDataPacket(data); err != nil {
			log.Printf("Failed to send packet to client %s: %v", destIP, err)
		} else {
			atomic.AddUint64(&s.stats.PacketsSent, 1)
			atomic.AddUint64(&s.stats.BytesSent, uint64(len(data)))
		}
	} else {
		log.Printf("No client found for destination IP: %s", destIP)
	}
}

// handleExternalPacket handles packets destined for external networks
func (s *Server) handleExternalPacket(data []byte, srcIP, destIP net.IP) {
	// In a real implementation, this would:
	// 1. Apply NAT rules
	// 2. Forward to external interface
	// 3. Handle return traffic

	log.Printf("External packet: %s -> %s (NAT/forwarding not fully implemented)", srcIP, destIP)

	// For now, we'll just log the packet
	// In production, this would involve iptables rules or similar
}

// GetClientCount returns the number of connected clients
func (s *Server) GetClientCount() int {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()
	return len(s.clients)
}

// GetServerStats returns server statistics
func (s *Server) GetServerStats() ServerStats {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	stats := ServerStats{
		ClientCount:   len(s.clients),
		AvailableIPs:  s.ipPool.GetAvailableCount(),
		AllocatedIPs:  s.ipPool.GetAllocatedCount(),
		ServerIP:      s.config.ServerIP,
		ServerNetwork: s.config.ServerNetwork,
		ListenAddress: s.config.ListenAddress,
		Port:          s.config.Port,
		Protocol:      s.config.Protocol,
	}

	return stats
}

// Allow checks if a request is allowed by the rate limiter
func (rl *RateLimiter) Allow() bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	elapsed := now.Sub(rl.lastRefill)

	// Refill tokens based on elapsed time
	tokensToAdd := int64(elapsed.Seconds() * float64(rl.refillRate))
	if tokensToAdd > 0 {
		rl.tokens += tokensToAdd
		if rl.tokens > rl.maxTokens {
			rl.tokens = rl.maxTokens
		}
		rl.lastRefill = now
	}

	// Check if we have tokens available
	if rl.tokens > 0 {
		rl.tokens--
		return true
	}

	return false
}

// Submit submits a task to the worker pool
func (wp *WorkerPool) Submit(task func()) {
	select {
	case wp.taskQueue <- task:
		// Task submitted successfully
	default:
		// Queue is full, execute immediately to avoid blocking
		go task()
	}
}

// Start starts the worker pool
func (wp *WorkerPool) Start() {
	for i := 0; i < wp.workers; i++ {
		wp.wg.Add(1)
		go wp.worker()
	}
}

// Stop stops the worker pool
func (wp *WorkerPool) Stop() {
	close(wp.stopChan)
	wp.wg.Wait()
}

// worker is the worker goroutine that processes tasks
func (wp *WorkerPool) worker() {
	defer wp.wg.Done()

	for {
		select {
		case task := <-wp.taskQueue:
			task()
		case <-wp.stopChan:
			return
		}
	}
}

// ServerStats represents server statistics
type ServerStats struct {
	ClientCount   int    `json:"client_count"`
	AvailableIPs  int    `json:"available_ips"`
	AllocatedIPs  int    `json:"allocated_ips"`
	ServerIP      string `json:"server_ip"`
	ServerNetwork string `json:"server_network"`
	ListenAddress string `json:"listen_address"`
	Port          int    `json:"port"`
	Protocol      string `json:"protocol"`
}
