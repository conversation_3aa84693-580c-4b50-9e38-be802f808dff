# Certificate Management

This directory contains scripts and tools for managing OpenVPN certificates.

## Quick Start

1. **Generate all certificates:**
   ```bash
   chmod +x generate-certs.sh
   ./generate-certs.sh
   ```

2. **Generate additional client certificates:**
   ```bash
   ./generate-certs.sh client john_doe
   ./generate-certs.sh client jane_smith
   ```

## Generated Files

After running the certificate generation script, you'll have:

### Server Certificates
- `ca.crt` - Certificate Authority certificate (public)
- `ca.key` - Certificate Authority private key (keep secure!)
- `server.crt` - Server certificate (public)
- `server.key` - Server private key (keep secure!)
- `dh2048.pem` - Di<PERSON>ie-<PERSON>man parameters (public)
- `ta.key` - TLS authentication key (shared secret)

### Client Certificates
- `client1.crt` - Sample client certificate
- `client1.key` - Sample client private key
- `client-template.ovpn` - Client configuration template

## Security Best Practices

### File Permissions
Set appropriate permissions on sensitive files:
```bash
chmod 600 *.key          # Private keys - owner read/write only
chmod 644 *.crt          # Certificates - world readable
chmod 644 *.pem          # DH parameters - world readable
chmod 600 ta.key         # TLS auth key - owner read/write only
```

### Key Management
1. **Backup your CA key securely** - Without it, you can't generate new certificates
2. **Store private keys securely** - Consider using a hardware security module (HSM)
3. **Use strong passphrases** - For additional protection of private keys
4. **Regular key rotation** - Replace certificates before they expire

### Certificate Revocation
To revoke a client certificate:
1. Add the certificate serial number to a Certificate Revocation List (CRL)
2. Configure the server to check the CRL
3. Distribute the updated CRL to all servers

## Client Configuration

### Creating Client Configuration Files

1. Copy the client template:
   ```bash
   cp client-template.ovpn client1.ovpn
   ```

2. Edit the configuration:
   - Replace `YOUR_SERVER_IP` with your server's IP address
   - Replace `CLIENT_NAME` with the actual client name
   - Adjust other settings as needed

3. For inline configuration (single file), embed certificates:
   ```bash
   # Add to client1.ovpn:
   <ca>
   [contents of ca.crt]
   </ca>
   <cert>
   [contents of client1.crt]
   </cert>
   <key>
   [contents of client1.key]
   </key>
   <tls-auth>
   [contents of ta.key]
   </tls-auth>
   ```

### Sample Client Configuration
```
client
dev tun
proto udp
remote vpn.example.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client1.crt
key client1.key
tls-auth ta.key 1
cipher AES-256-GCM
auth SHA256
verb 3
```

## Advanced Certificate Management

### Using Easy-RSA (Alternative)
If you prefer using Easy-RSA:
```bash
# Install Easy-RSA
git clone https://github.com/OpenVPN/easy-rsa.git
cd easy-rsa/easyrsa3

# Initialize PKI
./easyrsa init-pki

# Build CA
./easyrsa build-ca

# Generate server certificate
./easyrsa build-server-full server nopass

# Generate client certificate
./easyrsa build-client-full client1 nopass

# Generate DH parameters
./easyrsa gen-dh
```

### Certificate Validation
Verify certificate validity:
```bash
# Check certificate details
openssl x509 -in server.crt -text -noout

# Verify certificate chain
openssl verify -CAfile ca.crt server.crt

# Check certificate expiration
openssl x509 -in server.crt -noout -dates
```

### Troubleshooting

#### Common Issues
1. **Permission denied errors** - Check file permissions
2. **Certificate verification failed** - Ensure CA certificate is correct
3. **TLS handshake failed** - Check cipher compatibility
4. **Certificate expired** - Generate new certificates

#### Debug Commands
```bash
# Test certificate chain
openssl verify -CAfile ca.crt -verbose client1.crt

# Check private key matches certificate
openssl x509 -noout -modulus -in server.crt | openssl md5
openssl rsa -noout -modulus -in server.key | openssl md5

# View certificate details
openssl x509 -in server.crt -text -noout
```

## Production Considerations

### High Security Environments
1. Use hardware security modules (HSMs)
2. Implement certificate pinning
3. Use intermediate CAs
4. Regular security audits
5. Automated certificate renewal

### Scalability
1. Use certificate management systems
2. Automate client certificate generation
3. Implement certificate lifecycle management
4. Monitor certificate expiration

### Compliance
1. Follow organizational PKI policies
2. Maintain certificate audit logs
3. Implement proper key escrow procedures
4. Document certificate management procedures
