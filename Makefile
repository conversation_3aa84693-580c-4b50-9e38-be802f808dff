# OpenVPN Server Makefile

# Variables
BINARY_NAME=openvpn-server
BINARY_PATH=./cmd/server
BUILD_DIR=build
CERT_DIR=certs
CONFIG_DIR=configs
VERSION?=dev
COMMIT?=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME?=$(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=$(GOCMD) fmt

# Build flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.Commit=$(COMMIT) -X main.BuildTime=$(BUILD_TIME)"

# Default target
.PHONY: all
all: clean test build

# Build the binary
.PHONY: build
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(BINARY_PATH)
	@echo "Binary built: $(BUILD_DIR)/$(BINARY_NAME)"

# Build for multiple platforms
.PHONY: build-all
build-all: clean
	@echo "Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)
	
	# Linux AMD64
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(BINARY_PATH)
	
	# Linux ARM64
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-arm64 $(BINARY_PATH)
	
	# macOS AMD64
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(BINARY_PATH)
	
	# macOS ARM64 (Apple Silicon)
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(BINARY_PATH)
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(BINARY_PATH)
	
	@echo "Cross-platform binaries built in $(BUILD_DIR)/"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -cover ./...
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run benchmarks
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) ./...

# Lint code
.PHONY: lint
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# Vet code
.PHONY: vet
vet:
	@echo "Vetting code..."
	$(GOCMD) vet ./...

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# Download dependencies
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Generate certificates
.PHONY: certs
certs:
	@echo "Generating certificates..."
	@if [ ! -f $(CERT_DIR)/generate-certs.sh ]; then \
		echo "Certificate generation script not found!"; \
		exit 1; \
	fi
	@chmod +x $(CERT_DIR)/generate-certs.sh
	@cd $(CERT_DIR) && ./generate-certs.sh

# Generate client certificate
.PHONY: client-cert
client-cert:
	@if [ -z "$(CLIENT)" ]; then \
		echo "Usage: make client-cert CLIENT=<client_name>"; \
		exit 1; \
	fi
	@echo "Generating client certificate for: $(CLIENT)"
	@cd $(CERT_DIR) && ./generate-certs.sh client $(CLIENT)

# Install the binary
.PHONY: install
install: build
	@echo "Installing $(BINARY_NAME)..."
	@if [ "$(shell id -u)" != "0" ]; then \
		echo "Installation requires root privileges. Use: sudo make install"; \
		exit 1; \
	fi
	cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	chmod +x /usr/local/bin/$(BINARY_NAME)
	@echo "$(BINARY_NAME) installed to /usr/local/bin/"

# Uninstall the binary
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(BINARY_NAME)..."
	@if [ "$(shell id -u)" != "0" ]; then \
		echo "Uninstallation requires root privileges. Use: sudo make uninstall"; \
		exit 1; \
	fi
	rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "$(BINARY_NAME) uninstalled"

# Run the server (development)
.PHONY: run
run: build
	@echo "Running $(BINARY_NAME) in development mode..."
	@if [ "$(shell id -u)" != "0" ]; then \
		echo "Server requires root privileges. Use: sudo make run"; \
		exit 1; \
	fi
	$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_DIR)/server.conf

# Run with debug logging
.PHONY: run-debug
run-debug: build
	@echo "Running $(BINARY_NAME) with debug logging..."
	@if [ "$(shell id -u)" != "0" ]; then \
		echo "Server requires root privileges. Use: sudo make run-debug"; \
		exit 1; \
	fi
	$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_DIR)/server.conf -log-level debug

# Docker build
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t openvpn-server:$(VERSION) .
	docker tag openvpn-server:$(VERSION) openvpn-server:latest

# Docker run
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run --rm --privileged --cap-add=NET_ADMIN \
		-p 1194:1194/udp \
		-v $(PWD)/$(CONFIG_DIR):/app/$(CONFIG_DIR) \
		-v $(PWD)/$(CERT_DIR):/app/$(CERT_DIR) \
		openvpn-server:latest

# Create release package
.PHONY: package
package: build-all
	@echo "Creating release packages..."
	@mkdir -p $(BUILD_DIR)/packages
	
	# Linux package
	@mkdir -p $(BUILD_DIR)/openvpn-server-linux
	@cp $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(BUILD_DIR)/openvpn-server-linux/$(BINARY_NAME)
	@cp -r $(CONFIG_DIR) $(BUILD_DIR)/openvpn-server-linux/
	@cp -r $(CERT_DIR) $(BUILD_DIR)/openvpn-server-linux/
	@cp README.md $(BUILD_DIR)/openvpn-server-linux/
	@tar -czf $(BUILD_DIR)/packages/openvpn-server-linux-amd64.tar.gz -C $(BUILD_DIR) openvpn-server-linux
	
	# macOS package
	@mkdir -p $(BUILD_DIR)/openvpn-server-darwin
	@cp $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(BUILD_DIR)/openvpn-server-darwin/$(BINARY_NAME)
	@cp -r $(CONFIG_DIR) $(BUILD_DIR)/openvpn-server-darwin/
	@cp -r $(CERT_DIR) $(BUILD_DIR)/openvpn-server-darwin/
	@cp README.md $(BUILD_DIR)/openvpn-server-darwin/
	@tar -czf $(BUILD_DIR)/packages/openvpn-server-darwin-amd64.tar.gz -C $(BUILD_DIR) openvpn-server-darwin
	
	# Windows package
	@mkdir -p $(BUILD_DIR)/openvpn-server-windows
	@cp $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(BUILD_DIR)/openvpn-server-windows/$(BINARY_NAME).exe
	@cp -r $(CONFIG_DIR) $(BUILD_DIR)/openvpn-server-windows/
	@cp -r $(CERT_DIR) $(BUILD_DIR)/openvpn-server-windows/
	@cp README.md $(BUILD_DIR)/openvpn-server-windows/
	@cd $(BUILD_DIR) && zip -r packages/openvpn-server-windows-amd64.zip openvpn-server-windows
	
	@echo "Release packages created in $(BUILD_DIR)/packages/"

# Development setup
.PHONY: dev-setup
dev-setup: deps certs
	@echo "Development environment setup complete"
	@echo "Run 'make run' to start the server (requires root)"

# Check prerequisites
.PHONY: check
check:
	@echo "Checking prerequisites..."
	@command -v go >/dev/null 2>&1 || { echo "Go is not installed"; exit 1; }
	@command -v openssl >/dev/null 2>&1 || { echo "OpenSSL is not installed"; exit 1; }
	@echo "Prerequisites check passed"

# Show help
.PHONY: help
help:
	@echo "OpenVPN Server Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  build         Build the binary"
	@echo "  build-all     Build for multiple platforms"
	@echo "  test          Run tests"
	@echo "  test-coverage Run tests with coverage report"
	@echo "  bench         Run benchmarks"
	@echo "  fmt           Format code"
	@echo "  lint          Lint code (requires golangci-lint)"
	@echo "  vet           Vet code"
	@echo "  clean         Clean build artifacts"
	@echo "  deps          Download dependencies"
	@echo "  certs         Generate certificates"
	@echo "  client-cert   Generate client certificate (CLIENT=name)"
	@echo "  install       Install binary to /usr/local/bin (requires root)"
	@echo "  uninstall     Uninstall binary (requires root)"
	@echo "  run           Run server in development mode (requires root)"
	@echo "  run-debug     Run server with debug logging (requires root)"
	@echo "  docker-build  Build Docker image"
	@echo "  docker-run    Run Docker container"
	@echo "  package       Create release packages"
	@echo "  dev-setup     Set up development environment"
	@echo "  check         Check prerequisites"
	@echo "  help          Show this help message"
	@echo ""
	@echo "Examples:"
	@echo "  make build"
	@echo "  make client-cert CLIENT=alice"
	@echo "  sudo make run"
	@echo "  make docker-build && make docker-run"
