package protocol

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// Control message types
const (
	// TLS control messages
	TLS_HANDSHAKE          = 22
	TLS_ALERT              = 21
	TLS_CHANGE_CIPHER_SPEC = 20
	TLS_APPLICATION_DATA   = 23

	// OpenVPN specific control messages
	CONTROL_EXIT         = 0x01
	CONTROL_PUSH_REQUEST = 0x02
	CONTROL_PUSH_REPLY   = 0x03
	CONTROL_AUTH_REQUEST = 0x04
	CONTROL_AUTH_REPLY   = 0x05
)

// FragmentBuffer manages packet fragmentation and reassembly
type FragmentBuffer struct {
	fragments map[uint16][]byte
	totalSize uint32
	received  uint32
	timeout   time.Time
}

// ReplayWindow implements sliding window replay protection
type ReplayWindow struct {
	window uint64
	top    uint32
	size   uint32
	mutex  sync.Mutex
}

// ControlChannel manages the control channel for a client session
type ControlChannel struct {
	SessionID       []byte
	RemoteSessionID []byte
	PacketIDSend    uint32
	PacketIDRecv    uint32
	KeyID           uint8
	State           ControlState

	// Enhanced state management
	handshakeStartTime time.Time
	lastActivity       time.Time
	retransmitQueue    *ReliableQueue
	ackQueue           map[uint32]time.Time
	fragmentBuffer     map[uint32]*FragmentBuffer

	// Security features
	replayWindow *ReplayWindow
	maxPacketID  uint32

	// Performance metrics
	packetsReceived uint64
	packetsSent     uint64
	retransmissions uint64

	mutex sync.RWMutex
}

// ControlState represents the state of the control channel
type ControlState int

const (
	StateInit ControlState = iota
	StateTLSHandshake
	StateKeyExchange
	StateActive
	StateClosed
)

// String returns string representation of control state
func (s ControlState) String() string {
	switch s {
	case StateInit:
		return "INIT"
	case StateTLSHandshake:
		return "TLS_HANDSHAKE"
	case StateKeyExchange:
		return "KEY_EXCHANGE"
	case StateActive:
		return "ACTIVE"
	case StateClosed:
		return "CLOSED"
	default:
		return "UNKNOWN"
	}
}

// NewControlChannel creates a new control channel with enhanced features
func NewControlChannel(sessionID []byte) *ControlChannel {
	return &ControlChannel{
		SessionID:          sessionID,
		PacketIDSend:       1,
		PacketIDRecv:       0,
		KeyID:              0,
		State:              StateInit,
		handshakeStartTime: time.Now(),
		lastActivity:       time.Now(),
		retransmitQueue:    NewReliableQueue(5),
		ackQueue:           make(map[uint32]time.Time),
		fragmentBuffer:     make(map[uint32]*FragmentBuffer),
		replayWindow:       NewReplayWindow(64), // 64-bit sliding window
		maxPacketID:        0,
	}
}

// NewReplayWindow creates a new replay protection window
func NewReplayWindow(size uint32) *ReplayWindow {
	return &ReplayWindow{
		window: 0,
		top:    0,
		size:   size,
	}
}

// NewFragmentBuffer creates a new fragment buffer
func NewFragmentBuffer(totalSize uint32) *FragmentBuffer {
	return &FragmentBuffer{
		fragments: make(map[uint16][]byte),
		totalSize: totalSize,
		received:  0,
		timeout:   time.Now().Add(30 * time.Second), // 30 second timeout
	}
}

// CheckAndUpdate checks if a packet ID is valid and updates the replay window
func (rw *ReplayWindow) CheckAndUpdate(packetID uint32) bool {
	rw.mutex.Lock()
	defer rw.mutex.Unlock()

	// If this is the first packet or packet ID is newer than our window
	if rw.top == 0 || packetID > rw.top {
		// Shift window forward
		if rw.top > 0 {
			shift := packetID - rw.top
			if shift < rw.size {
				rw.window <<= shift
			} else {
				rw.window = 0
			}
		}
		rw.top = packetID
		rw.window |= 1 // Mark this packet as received
		return true
	}

	// Check if packet is within our window
	diff := rw.top - packetID
	if diff >= rw.size {
		// Packet is too old
		return false
	}

	// Check if we've already seen this packet
	mask := uint64(1) << diff
	if rw.window&mask != 0 {
		// Duplicate packet
		return false
	}

	// Mark packet as received
	rw.window |= mask
	return true
}

// ProcessControlPacket processes an incoming control packet with enhanced validation
func (cc *ControlChannel) ProcessControlPacket(packet *Packet) (*Packet, error) {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	// Update activity timestamp
	cc.lastActivity = time.Now()
	cc.packetsReceived++

	// Validate packet ID for replay protection
	if IsControlPacket(packet.Header.Opcode) && packet.Header.Opcode != P_ACK_V1 {
		if !cc.replayWindow.CheckAndUpdate(packet.Header.PacketID) {
			return nil, fmt.Errorf("replay attack detected: packet ID %d", packet.Header.PacketID)
		}
	}

	// Process based on opcode
	var response *Packet
	var err error

	switch packet.Header.Opcode {
	case P_CONTROL_HARD_RESET_CLIENT_V1, P_CONTROL_HARD_RESET_CLIENT_V2:
		response, err = cc.handleHardResetClient(packet)
	case P_CONTROL_V1:
		response, err = cc.handleControlMessage(packet)
	case P_ACK_V1:
		response, err = cc.handleAck(packet)
	default:
		return nil, fmt.Errorf("unsupported control opcode: %d", packet.Header.Opcode)
	}

	if err != nil {
		return nil, err
	}

	// Add to retransmit queue if response requires reliable delivery
	if response != nil && IsControlPacket(response.Header.Opcode) && response.Header.Opcode != P_ACK_V1 {
		cc.retransmitQueue.AddMessage(response, time.Now().Unix())
		cc.packetsSent++
	}

	return response, nil
}

// handleHardResetClient handles hard reset from client
func (cc *ControlChannel) handleHardResetClient(packet *Packet) (*Packet, error) {
	// Store remote session ID
	cc.RemoteSessionID = make([]byte, len(packet.Header.SessionID))
	copy(cc.RemoteSessionID, packet.Header.SessionID)

	// Reset state
	cc.State = StateTLSHandshake
	cc.PacketIDRecv = packet.Header.PacketID

	// Send hard reset server response
	var responseOpcode uint8
	if packet.Header.Opcode == P_CONTROL_HARD_RESET_CLIENT_V1 {
		responseOpcode = P_CONTROL_HARD_RESET_SERVER_V1
	} else {
		responseOpcode = P_CONTROL_HARD_RESET_SERVER_V2
	}

	response := NewControlPacket(
		responseOpcode,
		cc.KeyID,
		cc.SessionID,
		cc.PacketIDSend,
		nil,
	)
	cc.PacketIDSend++

	return response, nil
}

// handleControlMessage handles control messages
func (cc *ControlChannel) handleControlMessage(packet *Packet) (*Packet, error) {
	if len(packet.Payload) == 0 {
		return nil, fmt.Errorf("empty control message")
	}

	// Check if this is a TLS message or OpenVPN control message
	if packet.Payload[0] >= 20 && packet.Payload[0] <= 23 {
		// TLS message
		return cc.handleTLSMessage(packet)
	} else {
		// OpenVPN control message
		return cc.handleOpenVPNControl(packet)
	}
}

// handleTLSMessage handles TLS handshake messages
func (cc *ControlChannel) handleTLSMessage(packet *Packet) (*Packet, error) {
	tlsType := packet.Payload[0]

	switch tlsType {
	case TLS_HANDSHAKE:
		// For now, just acknowledge TLS handshake messages
		// In a real implementation, this would involve full TLS processing
		ack := NewAckPacket(cc.KeyID, cc.SessionID, packet.Header.PacketID)
		return ack, nil

	case TLS_APPLICATION_DATA:
		// TLS application data - this would contain OpenVPN control messages
		// after TLS handshake is complete
		cc.State = StateKeyExchange
		return cc.handleOpenVPNControl(packet)

	default:
		return nil, fmt.Errorf("unsupported TLS message type: %d", tlsType)
	}
}

// handleOpenVPNControl handles OpenVPN-specific control messages
func (cc *ControlChannel) handleOpenVPNControl(packet *Packet) (*Packet, error) {
	// Parse the control message from payload
	// This is a simplified implementation

	// Send ACK for the received packet
	ack := NewAckPacket(cc.KeyID, cc.SessionID, packet.Header.PacketID)

	// Check if this looks like a push request
	if len(packet.Payload) > 0 && strings.Contains(string(packet.Payload), "PUSH_REQUEST") {
		// Handle push request - send configuration to client
		pushReply := cc.createPushReply()
		response := NewControlPacket(
			P_CONTROL_V1,
			cc.KeyID,
			cc.SessionID,
			cc.PacketIDSend,
			pushReply,
		)
		cc.PacketIDSend++
		cc.State = StateActive
		return response, nil
	}

	return ack, nil
}

// handleAck handles ACK packets
func (cc *ControlChannel) handleAck(packet *Packet) (*Packet, error) {
	// Process ACK - in a real implementation, this would remove
	// acknowledged packets from retransmission queue
	return nil, nil
}

// createPushReply creates a PUSH_REPLY message with client configuration
func (cc *ControlChannel) createPushReply() []byte {
	// This is a simplified push reply
	// In a real implementation, this would be based on server configuration
	pushOptions := []string{
		"ifconfig 10.8.0.2 10.8.0.1",
		"route-gateway 10.8.0.1",
		"dhcp-option DNS 8.8.8.8",
		"dhcp-option DNS 8.8.4.4",
	}

	reply := "PUSH_REPLY," + strings.Join(pushOptions, ",")
	return []byte(reply)
}

// GetNextPacketID returns the next packet ID for sending
func (cc *ControlChannel) GetNextPacketID() uint32 {
	id := cc.PacketIDSend
	cc.PacketIDSend++
	return id
}

// IsValidPacketID checks if a received packet ID is valid (for replay protection)
func (cc *ControlChannel) IsValidPacketID(packetID uint32) bool {
	// Simple replay protection - in a real implementation,
	// this would use a sliding window
	if packetID > cc.PacketIDRecv {
		cc.PacketIDRecv = packetID
		return true
	}
	return false
}

// ReliableMessage represents a message that needs reliable delivery
type ReliableMessage struct {
	PacketID  uint32
	Packet    *Packet
	Timestamp int64
	Retries   int
}

// ReliableQueue manages reliable message delivery
type ReliableQueue struct {
	messages   map[uint32]*ReliableMessage
	maxRetries int
}

// NewReliableQueue creates a new reliable message queue
func NewReliableQueue(maxRetries int) *ReliableQueue {
	return &ReliableQueue{
		messages:   make(map[uint32]*ReliableMessage),
		maxRetries: maxRetries,
	}
}

// AddMessage adds a message to the reliable queue
func (rq *ReliableQueue) AddMessage(packet *Packet, timestamp int64) {
	msg := &ReliableMessage{
		PacketID:  packet.Header.PacketID,
		Packet:    packet,
		Timestamp: timestamp,
		Retries:   0,
	}
	rq.messages[packet.Header.PacketID] = msg
}

// AckMessage acknowledges a message (removes from queue)
func (rq *ReliableQueue) AckMessage(packetID uint32) {
	delete(rq.messages, packetID)
}

// GetRetransmissions returns messages that need retransmission
func (rq *ReliableQueue) GetRetransmissions(currentTime int64, timeout int64) []*Packet {
	var retrans []*Packet

	for _, msg := range rq.messages {
		if currentTime-msg.Timestamp > timeout {
			if msg.Retries < rq.maxRetries {
				msg.Retries++
				msg.Timestamp = currentTime
				retrans = append(retrans, msg.Packet)
			} else {
				// Max retries exceeded, remove message
				delete(rq.messages, msg.PacketID)
			}
		}
	}

	return retrans
}

// AddFragment adds a fragment to the buffer and returns complete packet if ready
func (fb *FragmentBuffer) AddFragment(fragmentID uint16, data []byte) ([]byte, bool) {
	fb.fragments[fragmentID] = data
	fb.received += uint32(len(data))

	// Check if we have all fragments
	if fb.received >= fb.totalSize {
		// Reassemble packet
		var result []byte
		for i := uint16(0); i < uint16(len(fb.fragments)); i++ {
			if fragment, exists := fb.fragments[i]; exists {
				result = append(result, fragment...)
			} else {
				// Missing fragment
				return nil, false
			}
		}
		return result, true
	}

	return nil, false
}

// IsExpired checks if the fragment buffer has expired
func (fb *FragmentBuffer) IsExpired() bool {
	return time.Now().After(fb.timeout)
}

// HandleFragmentation handles packet fragmentation for large control messages
func (cc *ControlChannel) HandleFragmentation(data []byte, maxSize int) []*Packet {
	if len(data) <= maxSize {
		// No fragmentation needed
		packet := NewControlPacket(P_CONTROL_V1, cc.KeyID, cc.SessionID, cc.GetNextPacketID(), data)
		return []*Packet{packet}
	}

	// Fragment the packet
	var fragments []*Packet
	fragmentID := uint16(0)

	for offset := 0; offset < len(data); offset += maxSize {
		end := offset + maxSize
		if end > len(data) {
			end = len(data)
		}

		// Create fragment header (simplified)
		fragmentData := make([]byte, 0, end-offset+4)
		fragmentData = append(fragmentData, byte(fragmentID>>8), byte(fragmentID))
		fragmentData = append(fragmentData, byte(len(data)>>8), byte(len(data)))
		fragmentData = append(fragmentData, data[offset:end]...)

		packet := NewControlPacket(P_CONTROL_V1, cc.KeyID, cc.SessionID, cc.GetNextPacketID(), fragmentData)
		fragments = append(fragments, packet)
		fragmentID++
	}

	return fragments
}

// ProcessFragment processes a fragmented packet
func (cc *ControlChannel) ProcessFragment(packet *Packet) ([]byte, bool, error) {
	if len(packet.Payload) < 4 {
		return nil, false, fmt.Errorf("fragment too short")
	}

	fragmentID := uint16(packet.Payload[0])<<8 | uint16(packet.Payload[1])
	totalSize := uint32(packet.Payload[2])<<8 | uint32(packet.Payload[3])
	fragmentData := packet.Payload[4:]

	// Get or create fragment buffer
	bufferKey := packet.Header.PacketID // Use packet ID as buffer key
	buffer, exists := cc.fragmentBuffer[bufferKey]
	if !exists {
		buffer = NewFragmentBuffer(totalSize)
		cc.fragmentBuffer[bufferKey] = buffer
	}

	// Check for expired buffers
	if buffer.IsExpired() {
		delete(cc.fragmentBuffer, bufferKey)
		return nil, false, fmt.Errorf("fragment buffer expired")
	}

	// Add fragment
	completeData, isComplete := buffer.AddFragment(fragmentID, fragmentData)
	if isComplete {
		delete(cc.fragmentBuffer, bufferKey)
		return completeData, true, nil
	}

	return nil, false, nil
}
