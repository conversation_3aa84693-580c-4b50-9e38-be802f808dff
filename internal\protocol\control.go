package protocol

import (
	"fmt"
	"strings"
)

// Control message types
const (
	// TLS control messages
	TLS_HANDSHAKE          = 22
	TLS_ALERT              = 21
	TLS_CHANGE_CIPHER_SPEC = 20
	TLS_APPLICATION_DATA   = 23

	// OpenVPN specific control messages
	CONTROL_EXIT         = 0x01
	CONTROL_PUSH_REQUEST = 0x02
	CONTROL_PUSH_REPLY   = 0x03
	CONTROL_AUTH_REQUEST = 0x04
	CONTROL_AUTH_REPLY   = 0x05
)

// ControlChannel manages the control channel for a client session
type ControlChannel struct {
	SessionID       []byte
	RemoteSessionID []byte
	PacketIDSend    uint32
	PacketIDRecv    uint32
	KeyID           uint8
	State           ControlState
}

// ControlState represents the state of the control channel
type ControlState int

const (
	StateInit ControlState = iota
	StateTLSHandshake
	StateKeyExchange
	StateActive
	StateClosed
)

// String returns string representation of control state
func (s ControlState) String() string {
	switch s {
	case StateInit:
		return "INIT"
	case StateTLSHandshake:
		return "TLS_HANDSHAKE"
	case StateKeyExchange:
		return "KEY_EXCHANGE"
	case StateActive:
		return "ACTIVE"
	case StateClosed:
		return "CLOSED"
	default:
		return "UNKNOWN"
	}
}

// NewControlChannel creates a new control channel
func NewControlChannel(sessionID []byte) *ControlChannel {
	return &ControlChannel{
		SessionID:    sessionID,
		PacketIDSend: 1,
		PacketIDRecv: 0,
		KeyID:        0,
		State:        StateInit,
	}
}

// ProcessControlPacket processes an incoming control packet
func (cc *ControlChannel) ProcessControlPacket(packet *Packet) (*Packet, error) {
	switch packet.Header.Opcode {
	case P_CONTROL_HARD_RESET_CLIENT_V1, P_CONTROL_HARD_RESET_CLIENT_V2:
		return cc.handleHardResetClient(packet)
	case P_CONTROL_V1:
		return cc.handleControlMessage(packet)
	case P_ACK_V1:
		return cc.handleAck(packet)
	default:
		return nil, fmt.Errorf("unsupported control opcode: %d", packet.Header.Opcode)
	}
}

// handleHardResetClient handles hard reset from client
func (cc *ControlChannel) handleHardResetClient(packet *Packet) (*Packet, error) {
	// Store remote session ID
	cc.RemoteSessionID = make([]byte, len(packet.Header.SessionID))
	copy(cc.RemoteSessionID, packet.Header.SessionID)

	// Reset state
	cc.State = StateTLSHandshake
	cc.PacketIDRecv = packet.Header.PacketID

	// Send hard reset server response
	var responseOpcode uint8
	if packet.Header.Opcode == P_CONTROL_HARD_RESET_CLIENT_V1 {
		responseOpcode = P_CONTROL_HARD_RESET_SERVER_V1
	} else {
		responseOpcode = P_CONTROL_HARD_RESET_SERVER_V2
	}

	response := NewControlPacket(
		responseOpcode,
		cc.KeyID,
		cc.SessionID,
		cc.PacketIDSend,
		nil,
	)
	cc.PacketIDSend++

	return response, nil
}

// handleControlMessage handles control messages
func (cc *ControlChannel) handleControlMessage(packet *Packet) (*Packet, error) {
	if len(packet.Payload) == 0 {
		return nil, fmt.Errorf("empty control message")
	}

	// Check if this is a TLS message or OpenVPN control message
	if packet.Payload[0] >= 20 && packet.Payload[0] <= 23 {
		// TLS message
		return cc.handleTLSMessage(packet)
	} else {
		// OpenVPN control message
		return cc.handleOpenVPNControl(packet)
	}
}

// handleTLSMessage handles TLS handshake messages
func (cc *ControlChannel) handleTLSMessage(packet *Packet) (*Packet, error) {
	tlsType := packet.Payload[0]

	switch tlsType {
	case TLS_HANDSHAKE:
		// For now, just acknowledge TLS handshake messages
		// In a real implementation, this would involve full TLS processing
		ack := NewAckPacket(cc.KeyID, cc.SessionID, packet.Header.PacketID)
		return ack, nil

	case TLS_APPLICATION_DATA:
		// TLS application data - this would contain OpenVPN control messages
		// after TLS handshake is complete
		cc.State = StateKeyExchange
		return cc.handleOpenVPNControl(packet)

	default:
		return nil, fmt.Errorf("unsupported TLS message type: %d", tlsType)
	}
}

// handleOpenVPNControl handles OpenVPN-specific control messages
func (cc *ControlChannel) handleOpenVPNControl(packet *Packet) (*Packet, error) {
	// Parse the control message from payload
	// This is a simplified implementation

	// Send ACK for the received packet
	ack := NewAckPacket(cc.KeyID, cc.SessionID, packet.Header.PacketID)

	// Check if this looks like a push request
	if len(packet.Payload) > 0 && strings.Contains(string(packet.Payload), "PUSH_REQUEST") {
		// Handle push request - send configuration to client
		pushReply := cc.createPushReply()
		response := NewControlPacket(
			P_CONTROL_V1,
			cc.KeyID,
			cc.SessionID,
			cc.PacketIDSend,
			pushReply,
		)
		cc.PacketIDSend++
		cc.State = StateActive
		return response, nil
	}

	return ack, nil
}

// handleAck handles ACK packets
func (cc *ControlChannel) handleAck(packet *Packet) (*Packet, error) {
	// Process ACK - in a real implementation, this would remove
	// acknowledged packets from retransmission queue
	return nil, nil
}

// createPushReply creates a PUSH_REPLY message with client configuration
func (cc *ControlChannel) createPushReply() []byte {
	// This is a simplified push reply
	// In a real implementation, this would be based on server configuration
	pushOptions := []string{
		"ifconfig 10.8.0.2 10.8.0.1",
		"route-gateway 10.8.0.1",
		"dhcp-option DNS 8.8.8.8",
		"dhcp-option DNS 8.8.4.4",
	}

	reply := "PUSH_REPLY," + strings.Join(pushOptions, ",")
	return []byte(reply)
}

// GetNextPacketID returns the next packet ID for sending
func (cc *ControlChannel) GetNextPacketID() uint32 {
	id := cc.PacketIDSend
	cc.PacketIDSend++
	return id
}

// IsValidPacketID checks if a received packet ID is valid (for replay protection)
func (cc *ControlChannel) IsValidPacketID(packetID uint32) bool {
	// Simple replay protection - in a real implementation,
	// this would use a sliding window
	if packetID > cc.PacketIDRecv {
		cc.PacketIDRecv = packetID
		return true
	}
	return false
}

// ReliableMessage represents a message that needs reliable delivery
type ReliableMessage struct {
	PacketID  uint32
	Packet    *Packet
	Timestamp int64
	Retries   int
}

// ReliableQueue manages reliable message delivery
type ReliableQueue struct {
	messages   map[uint32]*ReliableMessage
	maxRetries int
}

// NewReliableQueue creates a new reliable message queue
func NewReliableQueue(maxRetries int) *ReliableQueue {
	return &ReliableQueue{
		messages:   make(map[uint32]*ReliableMessage),
		maxRetries: maxRetries,
	}
}

// AddMessage adds a message to the reliable queue
func (rq *ReliableQueue) AddMessage(packet *Packet, timestamp int64) {
	msg := &ReliableMessage{
		PacketID:  packet.Header.PacketID,
		Packet:    packet,
		Timestamp: timestamp,
		Retries:   0,
	}
	rq.messages[packet.Header.PacketID] = msg
}

// AckMessage acknowledges a message (removes from queue)
func (rq *ReliableQueue) AckMessage(packetID uint32) {
	delete(rq.messages, packetID)
}

// GetRetransmissions returns messages that need retransmission
func (rq *ReliableQueue) GetRetransmissions(currentTime int64, timeout int64) []*Packet {
	var retrans []*Packet

	for _, msg := range rq.messages {
		if currentTime-msg.Timestamp > timeout {
			if msg.Retries < rq.maxRetries {
				msg.Retries++
				msg.Timestamp = currentTime
				retrans = append(retrans, msg.Packet)
			} else {
				// Max retries exceeded, remove message
				delete(rq.messages, msg.PacketID)
			}
		}
	}

	return retrans
}
