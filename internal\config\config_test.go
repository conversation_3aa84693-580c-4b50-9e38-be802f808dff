package config

import (
	"os"
	"testing"
	"time"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	if config.ListenAddress != "0.0.0.0" {
		t.<PERSON><PERSON><PERSON>("Expected default listen address '0.0.0.0', got '%s'", config.ListenAddress)
	}
	
	if config.Port != 1194 {
		t.<PERSON><PERSON><PERSON>("Expected default port 1194, got %d", config.Port)
	}
	
	if config.Protocol != "udp" {
		t.<PERSON><PERSON><PERSON>("Expected default protocol 'udp', got '%s'", config.Protocol)
	}
	
	if config.MaxClients != 100 {
		t.<PERSON><PERSON><PERSON>("Expected default max clients 100, got %d", config.MaxClients)
	}
	
	if config.Cipher != "AES-256-GCM" {
		t.<PERSON><PERSON>rf("Expected default cipher 'AES-256-GCM', got '%s'", config.Cipher)
	}
}

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file
	configContent := `# Test configuration
port 1195
proto tcp
local ***********
server ******** *************
dev tun0
ca test-ca.crt
cert test-server.crt
key test-server.key
dh test-dh.pem
cipher AES-128-GCM
auth SHA1
max-clients 50
keepalive 15 60
push "route *********** *************"
push "dhcp-option DNS *******"
client-to-client
duplicate-cn
comp-lzo
log test.log
verb 5
management 127.0.0.1 7506
`
	
	// Write to temporary file
	tmpFile, err := os.CreateTemp("", "openvpn-config-test-*.conf")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())
	
	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config: %v", err)
	}
	tmpFile.Close()
	
	// Load the config
	config, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	
	// Verify parsed values
	if config.Port != 1195 {
		t.Errorf("Expected port 1195, got %d", config.Port)
	}
	
	if config.Protocol != "tcp" {
		t.Errorf("Expected protocol 'tcp', got '%s'", config.Protocol)
	}
	
	if config.ListenAddress != "***********" {
		t.Errorf("Expected listen address '***********', got '%s'", config.ListenAddress)
	}
	
	if config.ServerNetwork != "********/*************" {
		t.Errorf("Expected server network '********/*************', got '%s'", config.ServerNetwork)
	}
	
	if config.DeviceType != "tun" {
		t.Errorf("Expected device type 'tun', got '%s'", config.DeviceType)
	}
	
	if config.DeviceName != "tun0" {
		t.Errorf("Expected device name 'tun0', got '%s'", config.DeviceName)
	}
	
	if config.CACertFile != "test-ca.crt" {
		t.Errorf("Expected CA cert file 'test-ca.crt', got '%s'", config.CACertFile)
	}
	
	if config.Cipher != "AES-128-GCM" {
		t.Errorf("Expected cipher 'AES-128-GCM', got '%s'", config.Cipher)
	}
	
	if config.Auth != "SHA1" {
		t.Errorf("Expected auth 'SHA1', got '%s'", config.Auth)
	}
	
	if config.MaxClients != 50 {
		t.Errorf("Expected max clients 50, got %d", config.MaxClients)
	}
	
	if config.KeepaliveInterval != 15*time.Second {
		t.Errorf("Expected keepalive interval 15s, got %v", config.KeepaliveInterval)
	}
	
	if config.KeepaliveTimeout != 60*time.Second {
		t.Errorf("Expected keepalive timeout 60s, got %v", config.KeepaliveTimeout)
	}
	
	if len(config.PushRoutes) != 1 || config.PushRoutes[0] != "*********** *************" {
		t.Errorf("Expected push route '*********** *************', got %v", config.PushRoutes)
	}
	
	if !config.ClientToClient {
		t.Error("Expected client-to-client to be true")
	}
	
	if !config.Duplicate {
		t.Error("Expected duplicate-cn to be true")
	}
	
	if !config.CompLZO {
		t.Error("Expected comp-lzo to be true")
	}
	
	if config.LogFile != "test.log" {
		t.Errorf("Expected log file 'test.log', got '%s'", config.LogFile)
	}
	
	if config.Verbosity != 5 {
		t.Errorf("Expected verbosity 5, got %d", config.Verbosity)
	}
	
	if config.ManagementIP != "127.0.0.1" {
		t.Errorf("Expected management IP '127.0.0.1', got '%s'", config.ManagementIP)
	}
	
	if config.ManagementPort != 7506 {
		t.Errorf("Expected management port 7506, got %d", config.ManagementPort)
	}
}

func TestConfigValidation(t *testing.T) {
	// Test valid config
	config := DefaultConfig()
	config.CACertFile = "ca.crt"
	config.ServerCertFile = "server.crt"
	config.ServerKeyFile = "server.key"
	config.DHParamFile = "dh.pem"
	
	if err := config.validate(); err != nil {
		t.Errorf("Valid config should not return error: %v", err)
	}
	
	// Test invalid port
	config.Port = 0
	if err := config.validate(); err == nil {
		t.Error("Invalid port should return error")
	}
	config.Port = 1194
	
	// Test invalid protocol
	config.Protocol = "invalid"
	if err := config.validate(); err == nil {
		t.Error("Invalid protocol should return error")
	}
	config.Protocol = "udp"
	
	// Test missing CA cert
	config.CACertFile = ""
	if err := config.validate(); err == nil {
		t.Error("Missing CA cert should return error")
	}
}

func TestParseLineErrors(t *testing.T) {
	config := DefaultConfig()
	
	// Test invalid port
	err := config.parseLine("port invalid", 1)
	if err == nil {
		t.Error("Invalid port should return error")
	}
	
	// Test invalid max-clients
	err = config.parseLine("max-clients abc", 1)
	if err == nil {
		t.Error("Invalid max-clients should return error")
	}
	
	// Test invalid keepalive
	err = config.parseLine("keepalive 10", 1)
	if err == nil {
		t.Error("Invalid keepalive (missing second argument) should return error")
	}
	
	err = config.parseLine("keepalive abc 60", 1)
	if err == nil {
		t.Error("Invalid keepalive interval should return error")
	}
	
	err = config.parseLine("keepalive 10 def", 1)
	if err == nil {
		t.Error("Invalid keepalive timeout should return error")
	}
}

func TestConfigComments(t *testing.T) {
	configContent := `# This is a comment
; This is also a comment
port 1194
# Another comment
proto udp
`
	
	tmpFile, err := os.CreateTemp("", "openvpn-config-comments-*.conf")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())
	
	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config: %v", err)
	}
	tmpFile.Close()
	
	config, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config with comments: %v", err)
	}
	
	if config.Port != 1194 {
		t.Errorf("Expected port 1194, got %d", config.Port)
	}
	
	if config.Protocol != "udp" {
		t.Errorf("Expected protocol 'udp', got '%s'", config.Protocol)
	}
}

func TestLoadNonExistentConfig(t *testing.T) {
	_, err := LoadConfig("non-existent-file.conf")
	if err == nil {
		t.Error("Loading non-existent config should return error")
	}
}
