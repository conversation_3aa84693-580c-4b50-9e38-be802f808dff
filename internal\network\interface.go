package network

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// Platform-specific interface creation timeout
const InterfaceCreationTimeout = 30 * time.Second

// TunTapInterface represents a TUN/TAP network interface
type TunTapInterface struct {
	Name    string
	Type    string // "tun" or "tap"
	IP      net.IP
	Netmask net.IPMask
	MTU     int
	file    *os.File
	isOpen  bool
}

// NewTunInterface creates a new TUN interface
func NewTunInterface(name string, ip net.IP, netmask net.IPMask, mtu int) (*TunTapInterface, error) {
	iface := &TunTapInterface{
		Name:    name,
		Type:    "tun",
		IP:      ip,
		Netmask: netmask,
		MTU:     mtu,
		isOpen:  false,
	}

	if err := iface.create(); err != nil {
		return nil, fmt.Errorf("failed to create TUN interface: %w", err)
	}

	return iface, nil
}

// NewTapInterface creates a new TAP interface
func NewTapInterface(name string, ip net.IP, netmask net.IPMask, mtu int) (*TunTapInterface, error) {
	iface := &TunTapInterface{
		Name:    name,
		Type:    "tap",
		IP:      ip,
		Netmask: netmask,
		MTU:     mtu,
		isOpen:  false,
	}

	if err := iface.create(); err != nil {
		return nil, fmt.Errorf("failed to create TAP interface: %w", err)
	}

	return iface, nil
}

// create creates the TUN/TAP interface
func (iface *TunTapInterface) create() error {
	switch runtime.GOOS {
	case "linux":
		return iface.createLinux()
	case "windows":
		return iface.createWindows()
	case "darwin":
		return iface.createDarwin()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// createLinux creates TUN/TAP interface on Linux
func (iface *TunTapInterface) createLinux() error {
	// Use ip tuntap command to create interface
	var cmd *exec.Cmd
	if iface.Type == "tun" {
		cmd = exec.Command("ip", "tuntap", "add", "dev", iface.Name, "mode", "tun")
	} else {
		cmd = exec.Command("ip", "tuntap", "add", "dev", iface.Name, "mode", "tap")
	}

	if err := cmd.Run(); err != nil {
		// Try alternative method with tunctl
		if iface.Type == "tun" {
			cmd = exec.Command("tunctl", "-t", iface.Name)
		} else {
			cmd = exec.Command("tunctl", "-t", iface.Name, "-p")
		}
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("failed to create %s interface %s: %w", iface.Type, iface.Name, err)
		}
	}

	// Open the interface for reading/writing
	devicePath := fmt.Sprintf("/sys/class/net/%s", iface.Name)
	if _, err := os.Stat(devicePath); err != nil {
		return fmt.Errorf("interface %s was not created properly: %w", iface.Name, err)
	}

	// Try to open the TUN device for packet I/O
	file, err := os.OpenFile("/dev/net/tun", os.O_RDWR, 0)
	if err != nil {
		log.Printf("Warning: could not open /dev/net/tun for packet I/O: %v", err)
	} else {
		iface.file = file
	}

	log.Printf("Created %s interface: %s", strings.ToUpper(iface.Type), iface.Name)

	return iface.configureInterface()
}

// createWindows creates TUN/TAP interface on Windows
func (iface *TunTapInterface) createWindows() error {
	// Windows requires TAP-Windows adapter to be installed
	// Check if TAP adapter is available
	cmd := exec.Command("netsh", "interface", "show", "interface")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to list network interfaces: %w", err)
	}

	// Look for TAP adapter
	if !strings.Contains(string(output), "TAP") {
		return fmt.Errorf("TAP-Windows adapter not found. Please install OpenVPN TAP driver")
	}

	// Create interface using netsh (simplified approach)
	// In production, this would use Windows API calls
	log.Printf("Windows TUN/TAP interface creation requires manual TAP adapter configuration")
	log.Printf("Please configure TAP adapter manually for interface: %s", iface.Name)

	return iface.configureInterface()
}

// createDarwin creates TUN/TAP interface on macOS
func (iface *TunTapInterface) createDarwin() error {
	// macOS uses utun interfaces for TUN
	if iface.Type == "tun" {
		// Try to create utun interface
		for i := 0; i < 10; i++ {
			utunName := fmt.Sprintf("utun%d", i)
			cmd := exec.Command("ifconfig", utunName, "create")
			if err := cmd.Run(); err == nil {
				iface.Name = utunName
				log.Printf("Created TUN interface: %s", utunName)
				return iface.configureInterface()
			}
		}
		return fmt.Errorf("failed to create utun interface")
	} else {
		// TAP interfaces require additional software on macOS
		return fmt.Errorf("TAP interfaces require TunTap for macOS to be installed")
	}
}

// configureInterface configures the network interface
func (iface *TunTapInterface) configureInterface() error {
	// Validate configuration
	if err := iface.validateConfiguration(); err != nil {
		return fmt.Errorf("invalid interface configuration: %w", err)
	}

	// Wait for interface to be available
	if err := iface.waitForInterface(); err != nil {
		return fmt.Errorf("interface not available: %w", err)
	}

	// Configure IP address
	if err := iface.setIPAddress(); err != nil {
		return fmt.Errorf("failed to set IP address: %w", err)
	}

	// Set MTU
	if err := iface.setMTU(); err != nil {
		return fmt.Errorf("failed to set MTU: %w", err)
	}

	// Bring interface up
	if err := iface.bringUp(); err != nil {
		return fmt.Errorf("failed to bring interface up: %w", err)
	}

	// Verify interface is operational
	if err := iface.verifyInterface(); err != nil {
		return fmt.Errorf("interface verification failed: %w", err)
	}

	iface.isOpen = true
	log.Printf("Interface %s configured successfully (IP: %s, MTU: %d)",
		iface.Name, iface.IP.String(), iface.MTU)

	return nil
}

// validateConfiguration validates the interface configuration
func (iface *TunTapInterface) validateConfiguration() error {
	if iface.Name == "" {
		return fmt.Errorf("interface name cannot be empty")
	}

	if iface.Type != "tun" && iface.Type != "tap" {
		return fmt.Errorf("invalid interface type: %s (must be 'tun' or 'tap')", iface.Type)
	}

	if iface.IP == nil {
		return fmt.Errorf("IP address cannot be nil")
	}

	if iface.Netmask == nil {
		return fmt.Errorf("netmask cannot be nil")
	}

	if iface.MTU <= 0 || iface.MTU > 65535 {
		return fmt.Errorf("invalid MTU: %d (must be between 1 and 65535)", iface.MTU)
	}

	return nil
}

// waitForInterface waits for the interface to become available
func (iface *TunTapInterface) waitForInterface() error {
	timeout := time.Now().Add(InterfaceCreationTimeout)

	for time.Now().Before(timeout) {
		if iface.interfaceExists() {
			return nil
		}
		time.Sleep(100 * time.Millisecond)
	}

	return fmt.Errorf("interface %s did not become available within %v", iface.Name, InterfaceCreationTimeout)
}

// interfaceExists checks if the interface exists in the system
func (iface *TunTapInterface) interfaceExists() bool {
	switch runtime.GOOS {
	case "linux":
		_, err := os.Stat(fmt.Sprintf("/sys/class/net/%s", iface.Name))
		return err == nil
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name)
		return cmd.Run() == nil
	case "windows":
		cmd := exec.Command("netsh", "interface", "show", "interface", iface.Name)
		return cmd.Run() == nil
	default:
		return false
	}
}

// verifyInterface verifies that the interface is operational
func (iface *TunTapInterface) verifyInterface() error {
	// Check if interface is up
	switch runtime.GOOS {
	case "linux", "darwin":
		cmd := exec.Command("ip", "link", "show", iface.Name)
		output, err := cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to check interface status: %w", err)
		}

		if !strings.Contains(string(output), "UP") {
			return fmt.Errorf("interface %s is not up", iface.Name)
		}

		// Check if IP is assigned
		cmd = exec.Command("ip", "addr", "show", iface.Name)
		output, err = cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to check interface IP: %w", err)
		}

		if !strings.Contains(string(output), iface.IP.String()) {
			return fmt.Errorf("IP address %s not assigned to interface %s", iface.IP.String(), iface.Name)
		}

	case "windows":
		// Windows-specific verification
		cmd := exec.Command("netsh", "interface", "ip", "show", "config", iface.Name)
		output, err := cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to check interface configuration: %w", err)
		}

		if !strings.Contains(string(output), iface.IP.String()) {
			log.Printf("Warning: IP verification on Windows may not be accurate")
		}
	}

	return nil
}

// setIPAddress sets the IP address of the interface
func (iface *TunTapInterface) setIPAddress() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "addr", "add",
			fmt.Sprintf("%s/%d", iface.IP.String(), maskToCIDR(iface.Netmask)),
			"dev", iface.Name)
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "inet",
			iface.IP.String(), "netmask", net.IP(iface.Netmask).String())
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for IP configuration: %s", runtime.GOOS)
	}
}

// setMTU sets the MTU of the interface
func (iface *TunTapInterface) setMTU() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "link", "set", "dev", iface.Name, "mtu", fmt.Sprintf("%d", iface.MTU))
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "mtu", fmt.Sprintf("%d", iface.MTU))
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for MTU configuration: %s", runtime.GOOS)
	}
}

// bringUp brings the interface up
func (iface *TunTapInterface) bringUp() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "link", "set", "dev", iface.Name, "up")
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "up")
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for interface up: %s", runtime.GOOS)
	}
}

// Read reads a packet from the interface with timeout and validation
func (iface *TunTapInterface) Read(buf []byte) (int, error) {
	if !iface.isOpen {
		return 0, fmt.Errorf("interface is not open")
	}

	if iface.file == nil {
		return 0, fmt.Errorf("interface file is not available")
	}

	// Set read timeout
	if err := iface.file.SetReadDeadline(time.Now().Add(5 * time.Second)); err != nil {
		log.Printf("Warning: failed to set read deadline: %v", err)
	}

	n, err := iface.file.Read(buf)
	if err != nil {
		return 0, fmt.Errorf("failed to read from interface: %w", err)
	}

	// Validate packet size
	if n == 0 {
		return 0, fmt.Errorf("received empty packet")
	}

	if n > iface.MTU {
		log.Printf("Warning: received packet larger than MTU (%d > %d)", n, iface.MTU)
	}

	return n, nil
}

// Write writes a packet to the interface with validation
func (iface *TunTapInterface) Write(buf []byte) (int, error) {
	if !iface.isOpen {
		return 0, fmt.Errorf("interface is not open")
	}

	if iface.file == nil {
		return 0, fmt.Errorf("interface file is not available")
	}

	// Validate packet size
	if len(buf) == 0 {
		return 0, fmt.Errorf("cannot write empty packet")
	}

	if len(buf) > iface.MTU {
		return 0, fmt.Errorf("packet size (%d) exceeds MTU (%d)", len(buf), iface.MTU)
	}

	// Set write timeout
	if err := iface.file.SetWriteDeadline(time.Now().Add(5 * time.Second)); err != nil {
		log.Printf("Warning: failed to set write deadline: %v", err)
	}

	n, err := iface.file.Write(buf)
	if err != nil {
		return 0, fmt.Errorf("failed to write to interface: %w", err)
	}

	if n != len(buf) {
		return n, fmt.Errorf("partial write: wrote %d of %d bytes", n, len(buf))
	}

	return n, nil
}

// ReadPacket reads a complete packet with proper framing
func (iface *TunTapInterface) ReadPacket() ([]byte, error) {
	buf := make([]byte, iface.MTU+100) // Extra space for headers
	n, err := iface.Read(buf)
	if err != nil {
		return nil, err
	}

	// Return only the actual packet data
	packet := make([]byte, n)
	copy(packet, buf[:n])

	return packet, nil
}

// WritePacket writes a complete packet
func (iface *TunTapInterface) WritePacket(packet []byte) error {
	_, err := iface.Write(packet)
	return err
}

// Close closes the interface with proper cleanup
func (iface *TunTapInterface) Close() error {
	if !iface.isOpen {
		return nil
	}

	log.Printf("Closing interface: %s", iface.Name)

	// Mark as closed first to prevent new operations
	iface.isOpen = false

	var errors []error

	// Close file descriptor
	if iface.file != nil {
		if err := iface.file.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close file descriptor: %w", err))
		}
		iface.file = nil
	}

	// Bring interface down
	if err := iface.bringDown(); err != nil {
		errors = append(errors, fmt.Errorf("failed to bring interface down: %w", err))
	}

	// Remove interface (platform-specific)
	if err := iface.removeInterface(); err != nil {
		errors = append(errors, fmt.Errorf("failed to remove interface: %w", err))
	}

	// Return combined errors if any
	if len(errors) > 0 {
		var errMsg strings.Builder
		errMsg.WriteString("interface cleanup errors: ")
		for i, err := range errors {
			if i > 0 {
				errMsg.WriteString("; ")
			}
			errMsg.WriteString(err.Error())
		}
		return fmt.Errorf("%s", errMsg.String())
	}

	log.Printf("Interface %s closed successfully", iface.Name)
	return nil
}

// bringDown brings the interface down
func (iface *TunTapInterface) bringDown() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "link", "set", "dev", iface.Name, "down")
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "down")
		return cmd.Run()
	case "windows":
		cmd := exec.Command("netsh", "interface", "set", "interface", iface.Name, "disabled")
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for bringing interface down: %s", runtime.GOOS)
	}
}

// removeInterface removes the interface from the system
func (iface *TunTapInterface) removeInterface() error {
	switch runtime.GOOS {
	case "linux":
		// Try ip command first
		cmd := exec.Command("ip", "tuntap", "del", "dev", iface.Name, "mode", iface.Type)
		if err := cmd.Run(); err != nil {
			// Try tunctl as fallback
			cmd = exec.Command("tunctl", "-d", iface.Name)
			return cmd.Run()
		}
		return nil
	case "darwin":
		// utun interfaces are automatically removed when closed
		return nil
	case "windows":
		// Windows TAP interfaces persist and need manual management
		log.Printf("Windows TAP interface %s should be manually disabled if no longer needed", iface.Name)
		return nil
	default:
		return fmt.Errorf("unsupported OS for removing interface: %s", runtime.GOOS)
	}
}

// GetName returns the interface name
func (iface *TunTapInterface) GetName() string {
	return iface.Name
}

// GetIP returns the interface IP address
func (iface *TunTapInterface) GetIP() net.IP {
	return iface.IP
}

// GetNetmask returns the interface netmask
func (iface *TunTapInterface) GetNetmask() net.IPMask {
	return iface.Netmask
}

// IsOpen returns true if the interface is open
func (iface *TunTapInterface) IsOpen() bool {
	return iface.isOpen
}

// maskToCIDR converts a netmask to CIDR notation
func maskToCIDR(mask net.IPMask) int {
	ones, _ := mask.Size()
	return ones
}

// MockTunInterface is a mock TUN interface for testing
type MockTunInterface struct {
	name      string
	ip        net.IP
	netmask   net.IPMask
	mtu       int
	isOpen    bool
	readChan  chan []byte
	writeChan chan []byte
}

// NewMockTunInterface creates a new mock TUN interface
func NewMockTunInterface(name string, ip net.IP, netmask net.IPMask, mtu int) *MockTunInterface {
	return &MockTunInterface{
		name:      name,
		ip:        ip,
		netmask:   netmask,
		mtu:       mtu,
		isOpen:    true,
		readChan:  make(chan []byte, 100),
		writeChan: make(chan []byte, 100),
	}
}

// Read reads a packet from the mock interface
func (m *MockTunInterface) Read(buf []byte) (int, error) {
	if !m.isOpen {
		return 0, fmt.Errorf("interface is closed")
	}

	select {
	case data := <-m.readChan:
		n := copy(buf, data)
		return n, nil
	default:
		return 0, fmt.Errorf("no data available")
	}
}

// Write writes a packet to the mock interface
func (m *MockTunInterface) Write(buf []byte) (int, error) {
	if !m.isOpen {
		return 0, fmt.Errorf("interface is closed")
	}

	data := make([]byte, len(buf))
	copy(data, buf)

	select {
	case m.writeChan <- data:
		return len(buf), nil
	default:
		return 0, fmt.Errorf("write buffer full")
	}
}

// Close closes the mock interface
func (m *MockTunInterface) Close() error {
	m.isOpen = false
	close(m.readChan)
	close(m.writeChan)
	return nil
}

// GetName returns the interface name
func (m *MockTunInterface) GetName() string {
	return m.name
}

// GetIP returns the interface IP address
func (m *MockTunInterface) GetIP() net.IP {
	return m.ip
}

// GetNetmask returns the interface netmask
func (m *MockTunInterface) GetNetmask() net.IPMask {
	return m.netmask
}

// IsOpen returns true if the interface is open
func (m *MockTunInterface) IsOpen() bool {
	return m.isOpen
}

// InjectPacket injects a packet into the read channel (for testing)
func (m *MockTunInterface) InjectPacket(data []byte) {
	if m.isOpen {
		select {
		case m.readChan <- data:
		default:
			// Buffer full, drop packet
		}
	}
}

// GetWrittenPacket gets a packet from the write channel (for testing)
func (m *MockTunInterface) GetWrittenPacket() []byte {
	select {
	case data := <-m.writeChan:
		return data
	default:
		return nil
	}
}
