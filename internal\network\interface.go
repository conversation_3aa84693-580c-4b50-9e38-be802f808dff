package network

import (
	"fmt"
	"net"
	"os"
	"os/exec"
	"runtime"
)

// TunTapInterface represents a TUN/TAP network interface
type TunTapInterface struct {
	Name    string
	Type    string // "tun" or "tap"
	IP      net.IP
	Netmask net.IPMask
	MTU     int
	file    *os.File
	isOpen  bool
}

// NewTunInterface creates a new TUN interface
func NewTunInterface(name string, ip net.IP, netmask net.IPMask, mtu int) (*TunTapInterface, error) {
	iface := &TunTapInterface{
		Name:    name,
		Type:    "tun",
		IP:      ip,
		Netmask: netmask,
		MTU:     mtu,
		isOpen:  false,
	}

	if err := iface.create(); err != nil {
		return nil, fmt.Errorf("failed to create TUN interface: %w", err)
	}

	return iface, nil
}

// NewTapInterface creates a new TAP interface
func NewTapInterface(name string, ip net.IP, netmask net.IPMask, mtu int) (*TunTapInterface, error) {
	iface := &TunTapInterface{
		Name:    name,
		Type:    "tap",
		IP:      ip,
		Netmask: netmask,
		MTU:     mtu,
		isOpen:  false,
	}

	if err := iface.create(); err != nil {
		return nil, fmt.Errorf("failed to create TAP interface: %w", err)
	}

	return iface, nil
}

// create creates the TUN/TAP interface
func (iface *TunTapInterface) create() error {
	switch runtime.GOOS {
	case "linux":
		return iface.createLinux()
	case "windows":
		return iface.createWindows()
	case "darwin":
		return iface.createDarwin()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// createLinux creates TUN/TAP interface on Linux
func (iface *TunTapInterface) createLinux() error {
	// This is a simplified implementation
	// In a real implementation, you would use the TUN/TAP driver

	// For now, we'll create a mock interface
	// In production, this would involve:
	// 1. Opening /dev/net/tun
	// 2. Setting up the interface with ioctl calls
	// 3. Configuring IP address and routing

	return iface.configureInterface()
}

// createWindows creates TUN/TAP interface on Windows
func (iface *TunTapInterface) createWindows() error {
	// Windows TUN/TAP creation would involve:
	// 1. Using TAP-Windows adapter
	// 2. Opening the device
	// 3. Configuring network settings

	return fmt.Errorf("Windows TUN/TAP interface creation not implemented")
}

// createDarwin creates TUN/TAP interface on macOS
func (iface *TunTapInterface) createDarwin() error {
	// macOS TUN/TAP creation would involve:
	// 1. Using utun interface
	// 2. System calls to create interface
	// 3. Network configuration

	return fmt.Errorf("macOS TUN/TAP interface creation not implemented")
}

// configureInterface configures the network interface
func (iface *TunTapInterface) configureInterface() error {
	// Configure IP address
	if err := iface.setIPAddress(); err != nil {
		return fmt.Errorf("failed to set IP address: %w", err)
	}

	// Set MTU
	if err := iface.setMTU(); err != nil {
		return fmt.Errorf("failed to set MTU: %w", err)
	}

	// Bring interface up
	if err := iface.bringUp(); err != nil {
		return fmt.Errorf("failed to bring interface up: %w", err)
	}

	iface.isOpen = true
	return nil
}

// setIPAddress sets the IP address of the interface
func (iface *TunTapInterface) setIPAddress() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "addr", "add",
			fmt.Sprintf("%s/%d", iface.IP.String(), maskToCIDR(iface.Netmask)),
			"dev", iface.Name)
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "inet",
			iface.IP.String(), "netmask", net.IP(iface.Netmask).String())
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for IP configuration: %s", runtime.GOOS)
	}
}

// setMTU sets the MTU of the interface
func (iface *TunTapInterface) setMTU() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "link", "set", "dev", iface.Name, "mtu", fmt.Sprintf("%d", iface.MTU))
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "mtu", fmt.Sprintf("%d", iface.MTU))
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for MTU configuration: %s", runtime.GOOS)
	}
}

// bringUp brings the interface up
func (iface *TunTapInterface) bringUp() error {
	switch runtime.GOOS {
	case "linux":
		cmd := exec.Command("ip", "link", "set", "dev", iface.Name, "up")
		return cmd.Run()
	case "darwin":
		cmd := exec.Command("ifconfig", iface.Name, "up")
		return cmd.Run()
	default:
		return fmt.Errorf("unsupported OS for interface up: %s", runtime.GOOS)
	}
}

// Read reads a packet from the interface
func (iface *TunTapInterface) Read(buf []byte) (int, error) {
	if !iface.isOpen {
		return 0, fmt.Errorf("interface is not open")
	}

	if iface.file == nil {
		return 0, fmt.Errorf("interface file is not available")
	}

	return iface.file.Read(buf)
}

// Write writes a packet to the interface
func (iface *TunTapInterface) Write(buf []byte) (int, error) {
	if !iface.isOpen {
		return 0, fmt.Errorf("interface is not open")
	}

	if iface.file == nil {
		return 0, fmt.Errorf("interface file is not available")
	}

	return iface.file.Write(buf)
}

// Close closes the interface
func (iface *TunTapInterface) Close() error {
	if !iface.isOpen {
		return nil
	}

	iface.isOpen = false

	if iface.file != nil {
		return iface.file.Close()
	}

	return nil
}

// GetName returns the interface name
func (iface *TunTapInterface) GetName() string {
	return iface.Name
}

// GetIP returns the interface IP address
func (iface *TunTapInterface) GetIP() net.IP {
	return iface.IP
}

// GetNetmask returns the interface netmask
func (iface *TunTapInterface) GetNetmask() net.IPMask {
	return iface.Netmask
}

// IsOpen returns true if the interface is open
func (iface *TunTapInterface) IsOpen() bool {
	return iface.isOpen
}

// maskToCIDR converts a netmask to CIDR notation
func maskToCIDR(mask net.IPMask) int {
	ones, _ := mask.Size()
	return ones
}

// MockTunInterface is a mock TUN interface for testing
type MockTunInterface struct {
	name      string
	ip        net.IP
	netmask   net.IPMask
	mtu       int
	isOpen    bool
	readChan  chan []byte
	writeChan chan []byte
}

// NewMockTunInterface creates a new mock TUN interface
func NewMockTunInterface(name string, ip net.IP, netmask net.IPMask, mtu int) *MockTunInterface {
	return &MockTunInterface{
		name:      name,
		ip:        ip,
		netmask:   netmask,
		mtu:       mtu,
		isOpen:    true,
		readChan:  make(chan []byte, 100),
		writeChan: make(chan []byte, 100),
	}
}

// Read reads a packet from the mock interface
func (m *MockTunInterface) Read(buf []byte) (int, error) {
	if !m.isOpen {
		return 0, fmt.Errorf("interface is closed")
	}

	select {
	case data := <-m.readChan:
		n := copy(buf, data)
		return n, nil
	default:
		return 0, fmt.Errorf("no data available")
	}
}

// Write writes a packet to the mock interface
func (m *MockTunInterface) Write(buf []byte) (int, error) {
	if !m.isOpen {
		return 0, fmt.Errorf("interface is closed")
	}

	data := make([]byte, len(buf))
	copy(data, buf)

	select {
	case m.writeChan <- data:
		return len(buf), nil
	default:
		return 0, fmt.Errorf("write buffer full")
	}
}

// Close closes the mock interface
func (m *MockTunInterface) Close() error {
	m.isOpen = false
	close(m.readChan)
	close(m.writeChan)
	return nil
}

// GetName returns the interface name
func (m *MockTunInterface) GetName() string {
	return m.name
}

// GetIP returns the interface IP address
func (m *MockTunInterface) GetIP() net.IP {
	return m.ip
}

// GetNetmask returns the interface netmask
func (m *MockTunInterface) GetNetmask() net.IPMask {
	return m.netmask
}

// IsOpen returns true if the interface is open
func (m *MockTunInterface) IsOpen() bool {
	return m.isOpen
}

// InjectPacket injects a packet into the read channel (for testing)
func (m *MockTunInterface) InjectPacket(data []byte) {
	if m.isOpen {
		select {
		case m.readChan <- data:
		default:
			// Buffer full, drop packet
		}
	}
}

// GetWrittenPacket gets a packet from the write channel (for testing)
func (m *MockTunInterface) GetWrittenPacket() []byte {
	select {
	case data := <-m.writeChan:
		return data
	default:
		return nil
	}
}
