package config

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config represents the server configuration
type Config struct {
	// Network settings
	ListenAddress string `json:"listen_address"`
	Port          int    `json:"port"`
	Protocol      string `json:"protocol"` // "udp" or "tcp"
	
	// VPN network settings
	ServerNetwork string `json:"server_network"` // e.g., "********/24"
	ServerIP      string `json:"server_ip"`      // e.g., "********"
	
	// TUN/TAP settings
	DeviceType string `json:"device_type"` // "tun" or "tap"
	DeviceName string `json:"device_name"` // optional device name
	
	// Certificate and key files
	CACertFile     string `json:"ca_cert_file"`
	ServerCertFile string `json:"server_cert_file"`
	ServerKeyFile  string `json:"server_key_file"`
	DHParamFile    string `json:"dh_param_file"`
	TLSAuthFile    string `json:"tls_auth_file"`
	
	// Encryption settings
	Cipher    string `json:"cipher"`    // e.g., "AES-256-GCM"
	Auth      string `json:"auth"`      // e.g., "SHA256"
	KeySize   int    `json:"key_size"`  // Key size in bits
	
	// Client settings
	MaxClients        int           `json:"max_clients"`
	ClientTimeout     time.Duration `json:"client_timeout"`
	KeepaliveInterval time.Duration `json:"keepalive_interval"`
	KeepaliveTimeout  time.Duration `json:"keepalive_timeout"`
	
	// Authentication
	AuthUserPass bool   `json:"auth_user_pass"` // Enable username/password auth
	AuthScript   string `json:"auth_script"`    // Script for user/pass validation
	
	// Routing
	PushRoutes    []string `json:"push_routes"`    // Routes to push to clients
	RedirectGW    bool     `json:"redirect_gw"`    // Redirect default gateway
	ClientToClient bool    `json:"client_to_client"` // Allow client-to-client communication
	
	// Logging
	LogFile   string `json:"log_file"`
	LogLevel  string `json:"log_level"`
	Verbosity int    `json:"verbosity"`
	
	// Advanced settings
	Duplicate bool `json:"duplicate_cn"` // Allow duplicate common names
	CompLZO   bool `json:"comp_lzo"`     // Enable LZO compression
	
	// Management interface
	ManagementIP   string `json:"management_ip"`
	ManagementPort int    `json:"management_port"`
}

// DefaultConfig returns a configuration with sensible defaults
func DefaultConfig() *Config {
	return &Config{
		ListenAddress:     "0.0.0.0",
		Port:              1194,
		Protocol:          "udp",
		ServerNetwork:     "********/24",
		ServerIP:          "********",
		DeviceType:        "tun",
		Cipher:            "AES-256-GCM",
		Auth:              "SHA256",
		KeySize:           256,
		MaxClients:        100,
		ClientTimeout:     60 * time.Second,
		KeepaliveInterval: 10 * time.Second,
		KeepaliveTimeout:  120 * time.Second,
		LogLevel:          "info",
		Verbosity:         3,
		CompLZO:           false,
		Duplicate:         false,
		RedirectGW:        false,
		ClientToClient:    false,
	}
}

// LoadConfig loads configuration from a file
func LoadConfig(filename string) (*Config, error) {
	config := DefaultConfig()
	
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open config file: %w", err)
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		
		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, ";") {
			continue
		}
		
		if err := config.parseLine(line, lineNum); err != nil {
			return nil, fmt.Errorf("error parsing line %d: %w", lineNum, err)
		}
	}
	
	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading config file: %w", err)
	}
	
	return config, config.validate()
}

// parseLine parses a single configuration line
func (c *Config) parseLine(line string, lineNum int) error {
	parts := strings.Fields(line)
	if len(parts) == 0 {
		return nil
	}
	
	directive := parts[0]
	args := parts[1:]
	
	switch directive {
	case "port":
		if len(args) != 1 {
			return fmt.Errorf("port requires exactly one argument")
		}
		port, err := strconv.Atoi(args[0])
		if err != nil {
			return fmt.Errorf("invalid port number: %s", args[0])
		}
		c.Port = port
		
	case "proto":
		if len(args) != 1 {
			return fmt.Errorf("proto requires exactly one argument")
		}
		if args[0] != "udp" && args[0] != "tcp" {
			return fmt.Errorf("proto must be 'udp' or 'tcp'")
		}
		c.Protocol = args[0]
		
	case "local":
		if len(args) != 1 {
			return fmt.Errorf("local requires exactly one argument")
		}
		c.ListenAddress = args[0]
		
	case "server":
		if len(args) != 2 {
			return fmt.Errorf("server requires exactly two arguments")
		}
		c.ServerNetwork = args[0] + "/" + args[1]
		
	case "dev":
		if len(args) != 1 {
			return fmt.Errorf("dev requires exactly one argument")
		}
		if strings.HasPrefix(args[0], "tun") {
			c.DeviceType = "tun"
			c.DeviceName = args[0]
		} else if strings.HasPrefix(args[0], "tap") {
			c.DeviceType = "tap"
			c.DeviceName = args[0]
		} else {
			return fmt.Errorf("unsupported device type: %s", args[0])
		}
		
	case "ca":
		if len(args) != 1 {
			return fmt.Errorf("ca requires exactly one argument")
		}
		c.CACertFile = args[0]
		
	case "cert":
		if len(args) != 1 {
			return fmt.Errorf("cert requires exactly one argument")
		}
		c.ServerCertFile = args[0]
		
	case "key":
		if len(args) != 1 {
			return fmt.Errorf("key requires exactly one argument")
		}
		c.ServerKeyFile = args[0]
		
	case "dh":
		if len(args) != 1 {
			return fmt.Errorf("dh requires exactly one argument")
		}
		c.DHParamFile = args[0]
		
	case "tls-auth":
		if len(args) < 1 || len(args) > 2 {
			return fmt.Errorf("tls-auth requires one or two arguments")
		}
		c.TLSAuthFile = args[0]
		
	case "cipher":
		if len(args) != 1 {
			return fmt.Errorf("cipher requires exactly one argument")
		}
		c.Cipher = args[0]
		
	case "auth":
		if len(args) != 1 {
			return fmt.Errorf("auth requires exactly one argument")
		}
		c.Auth = args[0]
		
	case "max-clients":
		if len(args) != 1 {
			return fmt.Errorf("max-clients requires exactly one argument")
		}
		maxClients, err := strconv.Atoi(args[0])
		if err != nil {
			return fmt.Errorf("invalid max-clients value: %s", args[0])
		}
		c.MaxClients = maxClients
		
	case "keepalive":
		if len(args) != 2 {
			return fmt.Errorf("keepalive requires exactly two arguments")
		}
		interval, err := strconv.Atoi(args[0])
		if err != nil {
			return fmt.Errorf("invalid keepalive interval: %s", args[0])
		}
		timeout, err := strconv.Atoi(args[1])
		if err != nil {
			return fmt.Errorf("invalid keepalive timeout: %s", args[1])
		}
		c.KeepaliveInterval = time.Duration(interval) * time.Second
		c.KeepaliveTimeout = time.Duration(timeout) * time.Second
		
	case "push":
		if len(args) < 1 {
			return fmt.Errorf("push requires at least one argument")
		}
		pushArg := strings.Join(args, " ")
		// Remove quotes if present
		if strings.HasPrefix(pushArg, "\"") && strings.HasSuffix(pushArg, "\"") {
			pushArg = pushArg[1 : len(pushArg)-1]
		}
		
		if strings.HasPrefix(pushArg, "route ") {
			c.PushRoutes = append(c.PushRoutes, pushArg[6:]) // Remove "route " prefix
		} else if pushArg == "redirect-gateway def1" {
			c.RedirectGW = true
		}
		
	case "client-to-client":
		c.ClientToClient = true
		
	case "duplicate-cn":
		c.Duplicate = true
		
	case "comp-lzo":
		c.CompLZO = true
		
	case "log":
		if len(args) != 1 {
			return fmt.Errorf("log requires exactly one argument")
		}
		c.LogFile = args[0]
		
	case "verb":
		if len(args) != 1 {
			return fmt.Errorf("verb requires exactly one argument")
		}
		verbosity, err := strconv.Atoi(args[0])
		if err != nil {
			return fmt.Errorf("invalid verbosity level: %s", args[0])
		}
		c.Verbosity = verbosity
		
	case "management":
		if len(args) != 2 {
			return fmt.Errorf("management requires exactly two arguments")
		}
		port, err := strconv.Atoi(args[1])
		if err != nil {
			return fmt.Errorf("invalid management port: %s", args[1])
		}
		c.ManagementIP = args[0]
		c.ManagementPort = port
		
	default:
		// Ignore unknown directives for now
		fmt.Printf("Warning: unknown directive '%s' on line %d\n", directive, lineNum)
	}
	
	return nil
}

// validate checks if the configuration is valid
func (c *Config) validate() error {
	if c.Port < 1 || c.Port > 65535 {
		return fmt.Errorf("invalid port number: %d", c.Port)
	}
	
	if c.Protocol != "udp" && c.Protocol != "tcp" {
		return fmt.Errorf("protocol must be 'udp' or 'tcp'")
	}
	
	if c.CACertFile == "" {
		return fmt.Errorf("ca certificate file is required")
	}
	
	if c.ServerCertFile == "" {
		return fmt.Errorf("server certificate file is required")
	}
	
	if c.ServerKeyFile == "" {
		return fmt.Errorf("server key file is required")
	}
	
	if c.DHParamFile == "" {
		return fmt.Errorf("DH parameters file is required")
	}
	
	if c.MaxClients < 1 {
		return fmt.Errorf("max-clients must be at least 1")
	}
	
	return nil
}
