package protocol

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"fmt"
	"io"
)

// CompressionType represents different compression algorithms
type CompressionType int

const (
	CompressionNone CompressionType = iota
	CompressionLZO
	CompressionLZ4
	CompressionGzip
	CompressionDeflate
)

// Compressor interface for different compression algorithms
type Compressor interface {
	Compress(data []byte) ([]byte, error)
	Decompress(data []byte) ([]byte, error)
	Type() CompressionType
}

// NoCompression implements no compression
type NoCompression struct{}

func (nc *NoCompression) Compress(data []byte) ([]byte, error) {
	return data, nil
}

func (nc *NoCompression) Decompress(data []byte) ([]byte, error) {
	return data, nil
}

func (nc *NoCompression) Type() CompressionType {
	return CompressionNone
}

// GzipCompression implements gzip compression
type GzipCompression struct {
	level int
}

func NewGzipCompression(level int) *GzipCompression {
	return &GzipCompression{level: level}
}

func (gc *GzipCompression) Compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	
	writer, err := gzip.NewWriterLevel(&buf, gc.level)
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip writer: %w", err)
	}
	
	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to write compressed data: %w", err)
	}
	
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close gzip writer: %w", err)
	}
	
	return buf.Bytes(), nil
}

func (gc *GzipCompression) Decompress(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer reader.Close()
	
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}
	
	return result, nil
}

func (gc *GzipCompression) Type() CompressionType {
	return CompressionGzip
}

// DeflateCompression implements deflate compression
type DeflateCompression struct {
	level int
}

func NewDeflateCompression(level int) *DeflateCompression {
	return &DeflateCompression{level: level}
}

func (dc *DeflateCompression) Compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	
	writer, err := flate.NewWriter(&buf, dc.level)
	if err != nil {
		return nil, fmt.Errorf("failed to create deflate writer: %w", err)
	}
	
	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to write compressed data: %w", err)
	}
	
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close deflate writer: %w", err)
	}
	
	return buf.Bytes(), nil
}

func (dc *DeflateCompression) Decompress(data []byte) ([]byte, error) {
	reader := flate.NewReader(bytes.NewReader(data))
	defer reader.Close()
	
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}
	
	return result, nil
}

func (dc *DeflateCompression) Type() CompressionType {
	return CompressionDeflate
}

// LZOCompression implements LZO compression (simplified version)
type LZOCompression struct{}

func NewLZOCompression() *LZOCompression {
	return &LZOCompression{}
}

func (lzo *LZOCompression) Compress(data []byte) ([]byte, error) {
	// LZO compression is not available in Go standard library
	// For production use, you would need to use a CGO binding to LZO library
	// For now, we'll use deflate as a fallback
	dc := NewDeflateCompression(flate.BestSpeed)
	return dc.Compress(data)
}

func (lzo *LZOCompression) Decompress(data []byte) ([]byte, error) {
	// LZO decompression fallback to deflate
	dc := NewDeflateCompression(flate.BestSpeed)
	return dc.Decompress(data)
}

func (lzo *LZOCompression) Type() CompressionType {
	return CompressionLZO
}

// CompressionManager manages compression for packets
type CompressionManager struct {
	compressor Compressor
	enabled    bool
	threshold  int // Minimum packet size to compress
}

// NewCompressionManager creates a new compression manager
func NewCompressionManager(compressionType CompressionType, threshold int) *CompressionManager {
	var compressor Compressor
	
	switch compressionType {
	case CompressionGzip:
		compressor = NewGzipCompression(gzip.BestSpeed)
	case CompressionDeflate:
		compressor = NewDeflateCompression(flate.BestSpeed)
	case CompressionLZO:
		compressor = NewLZOCompression()
	default:
		compressor = &NoCompression{}
	}
	
	return &CompressionManager{
		compressor: compressor,
		enabled:    compressionType != CompressionNone,
		threshold:  threshold,
	}
}

// CompressPacket compresses a packet if beneficial
func (cm *CompressionManager) CompressPacket(data []byte) ([]byte, bool, error) {
	if !cm.enabled || len(data) < cm.threshold {
		return data, false, nil
	}
	
	compressed, err := cm.compressor.Compress(data)
	if err != nil {
		return nil, false, fmt.Errorf("compression failed: %w", err)
	}
	
	// Only use compression if it actually reduces size
	if len(compressed) >= len(data) {
		return data, false, nil
	}
	
	return compressed, true, nil
}

// DecompressPacket decompresses a packet
func (cm *CompressionManager) DecompressPacket(data []byte) ([]byte, error) {
	if !cm.enabled {
		return data, nil
	}
	
	decompressed, err := cm.compressor.Decompress(data)
	if err != nil {
		return nil, fmt.Errorf("decompression failed: %w", err)
	}
	
	return decompressed, nil
}

// IsEnabled returns true if compression is enabled
func (cm *CompressionManager) IsEnabled() bool {
	return cm.enabled
}

// GetType returns the compression type
func (cm *CompressionManager) GetType() CompressionType {
	return cm.compressor.Type()
}

// SetEnabled enables or disables compression
func (cm *CompressionManager) SetEnabled(enabled bool) {
	cm.enabled = enabled
}

// SetThreshold sets the minimum packet size for compression
func (cm *CompressionManager) SetThreshold(threshold int) {
	cm.threshold = threshold
}

// GetCompressionStats returns compression statistics
func (cm *CompressionManager) GetCompressionStats() CompressionStats {
	// In a real implementation, this would track compression ratios,
	// bytes saved, etc.
	return CompressionStats{
		Type:      cm.compressor.Type(),
		Enabled:   cm.enabled,
		Threshold: cm.threshold,
	}
}

// CompressionStats holds compression statistics
type CompressionStats struct {
	Type           CompressionType
	Enabled        bool
	Threshold      int
	BytesOriginal  uint64
	BytesCompressed uint64
	PacketsCompressed uint64
	PacketsUncompressed uint64
}
