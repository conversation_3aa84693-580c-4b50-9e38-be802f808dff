# Multi-stage build for OpenVPN Server
FROM golang:1.19-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o openvpn-server ./cmd/server

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add \
    ca-certificates \
    openssl \
    iptables \
    iproute2 \
    && rm -rf /var/cache/apk/*

# Create non-root user (though we'll need root for TUN/TAP)
RUN addgroup -g 1001 openvpn && \
    adduser -D -s /bin/sh -u 1001 -G openvpn openvpn

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/openvpn-server .

# Copy configuration and certificate directories
COPY configs/ configs/
COPY certs/ certs/

# Create necessary directories
RUN mkdir -p /var/log/openvpn && \
    mkdir -p /etc/openvpn && \
    mkdir -p /dev/net

# Set permissions
RUN chmod +x openvpn-server && \
    chmod +x certs/generate-certs.sh && \
    chown -R openvpn:openvpn /app && \
    chown -R openvpn:openvpn /var/log/openvpn

# Expose OpenVPN port
EXPOSE 1194/udp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD pgrep openvpn-server || exit 1

# Default command
CMD ["./openvpn-server", "-config", "configs/server.conf"]

# Labels for metadata
LABEL maintainer="OpenVPN Server Team" \
      version="1.0" \
      description="OpenVPN Server implementation in Go" \
      org.opencontainers.image.source="https://github.com/your-org/openvpn-server"
